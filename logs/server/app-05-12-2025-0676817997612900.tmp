[SW_CTX: N/A] [INFO] 2025-05-12 09:50:30.825 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:30.875 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 9552 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 09:50:30.875 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:30.876 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:34.040 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:34.045 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:34.112 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 33 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:36.454 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:36.483 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:36.484 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:36.514 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:36.889 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#7675c171' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:36.897 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#2f9a10df' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:36.906 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#2dd1086' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.002 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.002 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.002 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.002 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.003 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.003 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.003 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.003 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.003 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.004 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.044 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.052 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.059 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.066 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.073 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.081 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.087 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.094 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.101 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.113 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.850 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.865 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.865 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:37.865 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:38.086 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:38.087 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 6979 ms
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:38.322 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:38.368 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:38.442 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 09:50:41.051 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:41.184 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:41.189 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:41.203 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:41.228 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@10fc1a22, ORIGINAL=[Ljava.lang.String;@1b841e7d, PIC_CLICK=[Ljava.lang.String;@6081f330]
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:41.228 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:41.429 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:41.566 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@28d56028, ORIGINAL=[Ljava.lang.String;@6c56fff, PIC_CLICK=[Ljava.lang.String;@3dee3a6c]
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:41.567 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:41.697 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 09:50:44.193 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 09:50:44.528 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:44.723 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:46.959 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:47.104 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:47.135 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 09:50:47.160 [main] com.byzp.platform.App:61 - Started App in 17.033 seconds (JVM running for 18.418)
[SW_CTX: N/A] [WARN] 2025-05-12 09:52:49.526 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:49.549 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:53.606 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:53.658 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 7668 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 09:52:53.659 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:53.659 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:56.110 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:56.112 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:56.147 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.570 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.589 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.590 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.613 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.899 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#1f536481' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.905 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#54c425b1' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.912 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#1c297897' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.978 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.979 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.979 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.979 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.979 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.979 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.980 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.980 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.980 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:57.980 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.010 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.016 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.021 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.026 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.032 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.037 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.043 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.047 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.053 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.058 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.548 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.558 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.558 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.559 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.740 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.741 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 4838 ms
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.906 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.936 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 09:52:58.973 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 09:53:00.683 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:00.794 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:00.801 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:00.806 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:00.830 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@6604f246, ORIGINAL=[Ljava.lang.String;@c1386b4, PIC_CLICK=[Ljava.lang.String;@53d9af1]
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:00.831 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:00.982 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:01.091 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@5fd4e67f, ORIGINAL=[Ljava.lang.String;@59b1edab, PIC_CLICK=[Ljava.lang.String;@3855b27e]
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:01.091 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:01.195 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 09:53:03.526 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 09:53:03.804 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:03.978 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:05.901 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:06.049 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:06.079 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 09:53:06.101 [main] com.byzp.platform.App:61 - Started App in 12.913 seconds (JVM running for 13.567)
[SW_CTX: N/A] [WARN] 2025-05-12 09:58:46.465 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:46.505 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:51.634 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:51.708 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 34192 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 09:58:51.708 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:51.709 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:54.035 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:54.038 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:54.078 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 25 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.513 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.531 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.532 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.552 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.823 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#54c425b1' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.829 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#16d0e521' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.835 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#6274f21c' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.901 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.902 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.902 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.902 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.902 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.902 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.902 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.902 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.903 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.903 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.932 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.938 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.943 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.949 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.955 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.961 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.966 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.971 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.976 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:55.980 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:56.498 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:56.508 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:56.508 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:56.509 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:56.680 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:56.680 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 4734 ms
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:56.842 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:56.886 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:56.921 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 09:58:58.673 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:58.780 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:58.787 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:58.792 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:58.810 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@4d5ea776, ORIGINAL=[Ljava.lang.String;@5d68be4f, PIC_CLICK=[Ljava.lang.String;@34eb5d01]
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:58.810 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:58.931 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:59.017 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@2b1a1a37, ORIGINAL=[Ljava.lang.String;@7d90764a, PIC_CLICK=[Ljava.lang.String;@6843fdc4]
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:59.018 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 09:58:59.093 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 09:59:01.270 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 09:59:01.497 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 09:59:01.637 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 09:59:03.495 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 09:59:03.674 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 09:59:03.709 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 09:59:03.734 [main] com.byzp.platform.App:61 - Started App in 12.488 seconds (JVM running for 13.177)
[SW_CTX: N/A] [WARN] 2025-05-12 10:04:15.390 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:15.413 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:20.615 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:20.665 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 34516 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:04:20.666 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:20.666 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:22.999 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:23.001 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:23.032 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.399 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.417 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.418 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.437 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.697 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#54c425b1' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.703 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#16d0e521' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.709 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#6274f21c' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.773 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.775 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.775 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.775 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.775 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.775 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.775 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.775 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.775 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.775 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.804 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.809 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.815 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.821 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.826 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.831 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.837 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.844 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.850 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:24.856 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:25.365 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:25.372 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:25.373 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:25.373 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:25.523 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:25.523 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 4560 ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:25.700 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:25.729 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:25.765 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 10:04:27.457 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:27.576 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:27.581 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:27.587 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:27.606 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@c89e263, ORIGINAL=[Ljava.lang.String;@4d5ea776, PIC_CLICK=[Ljava.lang.String;@5d68be4f]
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:27.607 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:27.737 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:27.845 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@5305f936, ORIGINAL=[Ljava.lang.String;@2b1a1a37, PIC_CLICK=[Ljava.lang.String;@7d90764a]
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:27.845 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:27.914 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:04:30.091 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:04:30.323 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:30.443 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:32.196 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:32.350 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:32.387 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 10:04:32.409 [main] com.byzp.platform.App:61 - Started App in 12.166 seconds (JVM running for 12.861)
[SW_CTX: N/A] [WARN] 2025-05-12 10:13:39.365 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:39.384 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:44.771 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:44.857 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 23792 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:13:44.857 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:44.858 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:48.211 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:48.219 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:48.250 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 18 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:49.786 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:49.805 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:49.807 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:49.830 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.132 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#76a7fcbd' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.139 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#446e7065' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.148 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#73608eb0' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.232 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.233 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.233 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.233 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.233 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.233 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.233 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.233 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.234 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.234 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.261 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.267 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.272 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.278 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.296 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.304 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.311 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.317 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.323 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:50.328 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:51.012 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:51.027 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:51.027 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:51.028 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:51.212 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:51.212 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 6047 ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:51.361 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:51.393 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:51.434 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 10:13:54.112 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:54.223 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:54.228 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:54.234 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:54.258 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@29db008c, ORIGINAL=[Ljava.lang.String;@1d008e61, PIC_CLICK=[Ljava.lang.String;@191b44ca]
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:54.258 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:54.424 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:54.537 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@54e18a46, ORIGINAL=[Ljava.lang.String;@1293f8d7, PIC_CLICK=[Ljava.lang.String;@58e5fbe5]
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:54.537 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:54.634 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:13:56.967 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:13:57.270 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:57.437 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:59.380 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:59.545 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:59.577 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 10:13:59.601 [main] com.byzp.platform.App:61 - Started App in 15.465 seconds (JVM running for 16.14)
[SW_CTX: N/A] [WARN] 2025-05-12 10:28:48.427 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:48.462 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:55.423 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:55.521 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 4308 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:28:55.522 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:55.523 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:58.068 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:58.071 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:58.106 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 21 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:59.652 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:59.672 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:59.673 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:59.695 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:59.971 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#22a260ff' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:59.977 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#2744dcae' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:28:59.983 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#1d6a8386' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.061 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.061 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.061 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.061 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.062 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.062 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.062 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.062 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.062 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.062 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.087 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.093 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.098 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.105 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.110 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.115 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.119 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.124 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.128 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.132 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.631 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.643 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.644 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.644 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.845 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:00.846 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 5039 ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:01.127 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:01.170 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:01.238 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 10:29:03.786 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:03.909 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:03.913 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:03.919 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:03.942 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@c1386b4, ORIGINAL=[Ljava.lang.String;@53d9af1, PIC_CLICK=[Ljava.lang.String;@c89e263]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:03.942 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:04.108 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:04.236 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@59b1edab, ORIGINAL=[Ljava.lang.String;@3855b27e, PIC_CLICK=[Ljava.lang.String;@5305f936]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:04.238 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:04.346 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:29:06.652 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:29:06.931 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:07.098 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:09.464 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:09.641 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:09.679 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:09.708 [main] com.byzp.platform.App:61 - Started App in 15.862 seconds (JVM running for 16.737)
[SW_CTX: N/A] [WARN] 2025-05-12 10:29:49.697 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:49.712 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:53.720 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:53.768 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 31496 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:29:53.769 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:53.770 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:56.022 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:56.024 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:56.060 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.523 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.542 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.543 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.566 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.880 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#33e0c716' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.887 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#35cec305' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.895 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#12952aff' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.991 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.991 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.991 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.992 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.992 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.992 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.992 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.992 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.992 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:57.993 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.031 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.039 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.045 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.053 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.061 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.068 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.075 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.083 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.090 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.096 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.835 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.848 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.848 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:58.849 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:59.127 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:59.127 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 5098 ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:59.364 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:59.415 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 10:29:59.474 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 10:30:02.361 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:02.510 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:02.517 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:02.524 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:02.548 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@552ffa44, ORIGINAL=[Ljava.lang.String;@6e66b498, PIC_CLICK=[Ljava.lang.String;@54d35ed5]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:02.549 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:02.734 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:02.884 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@771afdd5, ORIGINAL=[Ljava.lang.String;@3abadb65, PIC_CLICK=[Ljava.lang.String;@7131d668]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:02.885 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:03.000 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:30:05.720 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:30:06.002 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:06.158 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:08.177 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:08.363 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:08.400 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:08.435 [main] com.byzp.platform.App:61 - Started App in 15.209 seconds (JVM running for 15.883)
[SW_CTX: N/A] [WARN] 2025-05-12 10:30:28.046 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:28.087 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:33.067 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:33.180 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 34304 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:30:33.181 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:33.182 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:36.682 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:36.685 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:36.730 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 28 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:39.854 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:39.877 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:39.878 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:39.914 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.236 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#11ede87f' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.244 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#51cab489' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.252 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#28ee0a3c' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.341 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.342 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.342 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.342 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.342 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.343 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.343 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.343 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.343 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.343 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.380 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.388 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.395 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.402 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.410 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.417 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.426 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.433 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.443 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:40.456 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:41.296 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:41.309 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:41.310 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:41.310 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:41.590 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:41.590 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 8028 ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:41.811 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:41.859 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:41.916 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 10:30:44.440 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:44.569 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:44.576 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:44.583 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:44.603 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@248d2cec, ORIGINAL=[Ljava.lang.String;@5d77be8e, PIC_CLICK=[Ljava.lang.String;@55a055cc]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:44.604 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:44.772 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:44.895 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@123c48d0, ORIGINAL=[Ljava.lang.String;@14fff5e7, PIC_CLICK=[Ljava.lang.String;@1203d787]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:44.896 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:45.000 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:30:47.621 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:30:47.896 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:48.070 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:50.192 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:50.353 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:50.382 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 10:30:50.404 [main] com.byzp.platform.App:61 - Started App in 17.921 seconds (JVM running for 18.672)
[SW_CTX: N/A] [WARN] 2025-05-12 10:31:27.539 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:27.580 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:33.151 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:33.193 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 4368 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:31:33.193 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:33.194 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:35.441 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:35.443 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:35.481 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 23 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.176 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.200 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.201 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.227 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.595 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#1f536481' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.603 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#54c425b1' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.612 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#1c297897' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.721 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.722 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.722 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.722 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.723 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.723 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.723 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.723 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.724 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.724 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.784 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.794 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.803 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.812 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.820 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.828 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.838 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.847 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.857 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:37.864 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:38.833 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:38.852 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:38.853 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:38.854 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:39.171 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:39.172 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 5756 ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:39.680 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:39.740 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:39.800 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 10:31:42.890 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:43.030 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:43.036 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:43.042 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:43.068 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@6604f246, ORIGINAL=[Ljava.lang.String;@c1386b4, PIC_CLICK=[Ljava.lang.String;@53d9af1]
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:43.069 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:43.229 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:43.342 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@5fd4e67f, ORIGINAL=[Ljava.lang.String;@59b1edab, PIC_CLICK=[Ljava.lang.String;@3855b27e]
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:43.343 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:43.450 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:31:45.890 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:31:46.250 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:46.450 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:48.625 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:48.778 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:48.808 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:48.831 [main] com.byzp.platform.App:61 - Started App in 16.109 seconds (JVM running for 16.711)
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:58.443 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=/client/conn_status,Qos=1,payload={"msgType": "client.disconnected", "clientId": "mqttx_b17d632d", "time": "1747017118440"}
[SW_CTX: N/A] [INFO] 2025-05-12 10:31:58.605 [MQTT Call: QXL000111222333] com.alibaba.druid.pool.DruidDataSource:947 - {dataSource-1} inited
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:31:58.862 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:31:58.887 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: mqttx_b17d632d(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:31:58.930 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:32:08.271 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=/client/conn_status,Qos=1,payload={"msgType": "client.connected", "clientId": "mqttx_b17d632d", "time": "1747017128274"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:32:08.272 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:32:08.273 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: mqttx_b17d632d(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:32:08.282 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [WARN] 2025-05-12 10:33:20.973 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:20.996 [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource:1863 - {dataSource-1} closed
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:20.996 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:27.459 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:27.522 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 20388 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:27.523 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:27.524 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:29.937 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:29.945 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:29.975 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 19 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.373 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.390 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.392 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.410 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.688 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#6b8d54da' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.694 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4b41587d' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.700 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#1f536481' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.767 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.767 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.768 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.768 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.768 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.768 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.768 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.768 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.768 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.769 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.799 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.806 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.811 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.816 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.822 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.827 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.832 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.837 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.843 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:31.848 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:32.337 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:32.346 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:32.346 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:32.346 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:32.569 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:32.569 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 4757 ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:32.783 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:32.833 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:32.874 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 10:33:35.003 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:35.154 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:35.162 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:35.168 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:35.199 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@e7b3e54, ORIGINAL=[Ljava.lang.String;@78d61f17, PIC_CLICK=[Ljava.lang.String;@4cfe9594]
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:35.199 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:35.391 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:35.548 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@3bf26810, ORIGINAL=[Ljava.lang.String;@19213a74, PIC_CLICK=[Ljava.lang.String;@30a791a6]
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:35.548 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:35.682 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:37.424 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=client/conn_status,Qos=1,payload={"msgType": "client.disconnected", "clientId": "QXL000111222333", "time": "1747017201381"}
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:37.958 [MQTT Call: QXL000111222333] com.alibaba.druid.pool.DruidDataSource:947 - {dataSource-1} inited
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.207 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.341 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.402 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: QXL000111222333(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.455 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:38.460 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=client/conn_status,Qos=1,payload={"msgType": "client.disconnected", "clientId": "QXL000111222333", "time": "1747017216952"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.465 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.467 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: QXL000111222333(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.481 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:38.482 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=client/conn_status,Qos=1,payload={"msgType": "client.connected", "clientId": "QXL000111222333", "time": "1747017216955"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.484 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.485 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: QXL000111222333(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.497 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:38.498 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=client/conn_status,Qos=1,payload={"msgType": "client.disconnected", "clientId": "QXL000111222333", "time": "1747017217412"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.501 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.502 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: QXL000111222333(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.514 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:38.515 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=client/conn_status,Qos=1,payload={"msgType": "client.connected", "clientId": "QXL000111222333", "time": "1747017217415"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.518 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.518 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: QXL000111222333(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.531 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:33:38.550 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:38.752 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:40.775 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:40.985 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:41.017 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 10:33:41.043 [main] com.byzp.platform.App:61 - Started App in 14.714 seconds (JVM running for 15.577)
[SW_CTX: N/A] [WARN] 2025-05-12 10:34:33.919 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:33.957 [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource:1863 - {dataSource-1} closed
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:33.958 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:40.459 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:40.511 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 35052 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:34:40.512 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:40.512 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:43.362 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:43.367 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:43.412 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 26 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.272 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.296 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.297 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.324 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.645 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#5234b61a' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.653 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#50b734c4' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.661 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#33e0c716' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.749 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.750 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.750 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.750 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.750 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.750 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.751 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.751 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.751 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.751 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.788 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.795 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.803 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.810 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.816 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.824 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.831 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.838 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.844 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:45.852 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:46.510 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:46.525 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:46.526 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:46.526 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:46.760 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:46.761 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 6008 ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:46.945 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:46.988 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:47.060 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 10:34:49.589 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:49.719 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:49.735 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:49.744 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:49.772 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@6604f246, ORIGINAL=[Ljava.lang.String;@c1386b4, PIC_CLICK=[Ljava.lang.String;@53d9af1]
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:49.773 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:49.976 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:50.118 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@5fd4e67f, ORIGINAL=[Ljava.lang.String;@59b1edab, PIC_CLICK=[Ljava.lang.String;@3855b27e]
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:50.118 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:50.243 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:34:52.650 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:34:52.926 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:53.099 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:55.024 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:55.176 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:55.205 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 10:34:55.226 [main] com.byzp.platform.App:61 - Started App in 15.438 seconds (JVM running for 16.499)
[SW_CTX: N/A] [INFO] 2025-05-12 10:35:12.466 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.disconnected", "clientId": "mqttx_b17d632d", "time": "1747017312462"}
[SW_CTX: N/A] [INFO] 2025-05-12 10:35:12.732 [MQTT Call: QXL000111222333] com.alibaba.druid.pool.DruidDataSource:947 - {dataSource-1} inited
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:35:12.986 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:35:13.080 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: mqttx_b17d632d(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:35:13.133 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:35:17.866 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.connected", "clientId": "mqttx_b17d632d", "time": "1747017317866"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:35:17.867 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:35:17.868 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: mqttx_b17d632d(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:35:17.878 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:41:33.409 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.connected", "clientId": "paho12327613053560126", "time": "1747017693408"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:41:33.430 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:41:33.432 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: paho12327613053560126(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:41:33.444 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:41:33.808 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.disconnected", "clientId": "paho12327613053560126", "time": "1747017693807"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:41:33.813 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:41:33.814 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: paho12327613053560126(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:41:33.827 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:41:33.829 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.connected", "clientId": "paho12327613053560126", "time": "1747017693809"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:41:33.832 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:41:33.834 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: paho12327613053560126(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:41:33.846 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:42:59.038 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.connected", "clientId": "30eda02b8878", "time": "1747017779038"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:42:59.050 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:42:59.050 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: 30eda02b8878(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:42:59.067 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 1
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:42:59.069 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudApplicationMapper.selectById:137 - ==>  Preparing: SELECT id,cloud_user_id,app_name,remark,notice_callback_url,app_secret,app_id,voice_similarity,create_time,update_time FROM cloud_application WHERE id=?
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:42:59.069 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudApplicationMapper.selectById:137 - ==> Parameters: 5(Long)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:42:59.080 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudApplicationMapper.selectById:137 - <==      Total: 1
[SW_CTX: N/A] [INFO] 2025-05-12 10:42:59.189 [MQTT Call: QXL000111222333] com.dtflys.forest.config.ForestConfiguration:392 - [Forest] Http Backend: okhttp3
[SW_CTX: N/A] [INFO] 2025-05-12 10:42:59.245 [MQTT Call: QXL000111222333] com.dtflys.forest.logging.DefaultLogHandler:21 - [Forest] Request (okhttp3): 
	POST https://test-xpky.pin-bike.com/api/alarm/callback HTTPS
	Headers: 
		msgSource: byzp
		Content-Type: application/json
	Body: {"deviceSerial":"30eda02b8878","msgType":"client.connected","data":{},"msgId":"6c57ab74c3a0468dab07573f25d5fb41","msgTime":1747017779038}
[SW_CTX: N/A] [INFO] 2025-05-12 10:42:59.444 [MQTT Call: QXL000111222333] com.dtflys.forest.logging.DefaultLogHandler:21 - [Forest] Response: Status = 200, Time = 198ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:42:59.454 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:124 - 设备在线:{"code":0,"success":true}
[SW_CTX: N/A] [INFO] 2025-05-12 10:46:30.753 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.disconnected", "clientId": "mqttx_b17d632d", "time": "1747017990753"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:46:30.764 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:46:30.765 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: mqttx_b17d632d(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:46:30.775 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 10:47:29.127 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.disconnected", "clientId": "30eda02b8878", "time": "1747018049126"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.131 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.133 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: 30eda02b8878(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.146 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 1
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.147 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudApplicationMapper.selectById:137 - ==>  Preparing: SELECT id,cloud_user_id,app_name,remark,notice_callback_url,app_secret,app_id,voice_similarity,create_time,update_time FROM cloud_application WHERE id=?
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.148 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudApplicationMapper.selectById:137 - ==> Parameters: 5(Long)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.159 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudApplicationMapper.selectById:137 - <==      Total: 1
[SW_CTX: N/A] [INFO] 2025-05-12 10:47:29.161 [MQTT Call: QXL000111222333] com.dtflys.forest.logging.DefaultLogHandler:21 - [Forest] Request (okhttp3): 
	POST https://test-xpky.pin-bike.com/api/alarm/callback HTTPS
	Headers: 
		msgSource: byzp
		Content-Type: application/json
	Body: {"deviceSerial":"30eda02b8878","msgType":"client.disConnected","data":{},"msgId":"5a230dc6333841c09d8f353eda7e1651","msgTime":1747018049126}
[SW_CTX: N/A] [INFO] 2025-05-12 10:47:29.263 [MQTT Call: QXL000111222333] com.dtflys.forest.logging.DefaultLogHandler:21 - [Forest] Response: Status = 200, Time = 102ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:47:29.264 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:138 - 设备离线:{"code":0,"success":true}
[SW_CTX: N/A] [INFO] 2025-05-12 10:47:29.264 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.connected", "clientId": "30eda02b8878", "time": "1747018049129"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.265 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.266 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: 30eda02b8878(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.279 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 1
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.280 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudApplicationMapper.selectById:137 - ==>  Preparing: SELECT id,cloud_user_id,app_name,remark,notice_callback_url,app_secret,app_id,voice_similarity,create_time,update_time FROM cloud_application WHERE id=?
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.280 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudApplicationMapper.selectById:137 - ==> Parameters: 5(Long)
[SW_CTX: N/A] [DEBUG] 2025-05-12 10:47:29.293 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudApplicationMapper.selectById:137 - <==      Total: 1
[SW_CTX: N/A] [INFO] 2025-05-12 10:47:29.296 [MQTT Call: QXL000111222333] com.dtflys.forest.logging.DefaultLogHandler:21 - [Forest] Request (okhttp3): 
	POST https://test-xpky.pin-bike.com/api/alarm/callback HTTPS
	Headers: 
		msgSource: byzp
		Content-Type: application/json
	Body: {"deviceSerial":"30eda02b8878","msgType":"client.connected","data":{},"msgId":"2b0c33ba245643f9a37e44e1ad3d12d5","msgTime":1747018049129}
[SW_CTX: N/A] [INFO] 2025-05-12 10:47:29.406 [MQTT Call: QXL000111222333] com.dtflys.forest.logging.DefaultLogHandler:21 - [Forest] Response: Status = 200, Time = 110ms
[SW_CTX: N/A] [INFO] 2025-05-12 10:47:29.406 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:124 - 设备在线:{"code":0,"success":true}
[SW_CTX: N/A] [WARN] 2025-05-12 10:48:20.633 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 10:48:20.659 [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource:1863 - {dataSource-1} closed
[SW_CTX: N/A] [INFO] 2025-05-12 10:48:20.659 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 14:46:57.001 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 14:46:57.261 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 5040 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:46:57.263 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 14:46:57.264 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:04.427 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:04.439 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:04.517 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 44 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:09.922 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:09.963 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:09.964 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:10.009 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.246 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#4776e209' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.257 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#5234b61a' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.268 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#b14b60a' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.650 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.652 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.652 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.652 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.654 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.655 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.655 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.656 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.657 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.657 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.725 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.736 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.744 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.751 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.760 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.767 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.777 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.786 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.795 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:11.805 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:13.965 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:13.992 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:14.035 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:14.035 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:14.540 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:14.541 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 16291 ms
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:14.965 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:15.049 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:15.163 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 14:47:22.005 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:22.428 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:22.440 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:22.456 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:22.543 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@6b103db7, ORIGINAL=[Ljava.lang.String;@b3042ed, PIC_CLICK=[Ljava.lang.String;@1f12d5e0]
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:22.544 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:22.904 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:23.065 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@6abd8bcd, ORIGINAL=[Ljava.lang.String;@70cd122, PIC_CLICK=[Ljava.lang.String;@79424f25]
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:23.066 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:23.236 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:47:26.905 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:47:27.435 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:27.639 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:31.721 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:31.978 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:32.030 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:32.150 [main] com.byzp.platform.App:61 - Started App in 36.69 seconds (JVM running for 38.97)
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:52.036 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.connected", "clientId": "mqttx_qxl", "time": "1747032472644"}
[SW_CTX: N/A] [INFO] 2025-05-12 14:47:52.735 [MQTT Call: QXL000111222333] com.alibaba.druid.pool.DruidDataSource:947 - {dataSource-1} inited
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:47:53.676 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:47:53.770 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: mqttx_qxl(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:47:53.825 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [WARN] 2025-05-12 14:48:29.761 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 14:48:29.816 [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource:1863 - {dataSource-1} closed
[SW_CTX: N/A] [INFO] 2025-05-12 14:48:29.817 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 14:48:50.554 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 14:48:50.768 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 5192 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:48:50.769 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 14:48:50.771 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:18.435 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:18.699 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 27640 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:49:18.701 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:18.702 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:43.690 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:43.759 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 19076 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:49:43.760 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:43.760 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:48.593 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:48.600 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:48.735 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 92 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:52.345 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:52.420 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:52.422 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:52.464 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.015 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#5234b61a' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.028 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#50b734c4' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.046 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#33e0c716' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.209 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.210 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.210 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.211 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.211 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.212 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.212 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.212 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.212 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.212 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.259 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.268 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.277 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.286 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.295 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.306 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.315 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.323 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.329 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:53.335 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:54.805 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:54.824 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:54.825 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:54.826 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:55.152 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:55.152 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 10971 ms
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:55.598 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:55.667 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 14:49:55.742 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 14:50:01.560 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:01.756 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:01.766 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:01.781 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:01.830 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@c1386b4, ORIGINAL=[Ljava.lang.String;@53d9af1, PIC_CLICK=[Ljava.lang.String;@c89e263]
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:01.831 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:02.271 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:02.471 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@59b1edab, ORIGINAL=[Ljava.lang.String;@3855b27e, PIC_CLICK=[Ljava.lang.String;@5305f936]
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:02.472 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:02.693 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:06.459 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.disconnected", "clientId": "QXL000111222333", "time": "1747032510906"}
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:06.732 [MQTT Call: QXL000111222333] com.alibaba.druid.pool.DruidDataSource:947 - {dataSource-1} inited
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:07.993 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:08.094 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:08.180 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: QXL000111222333(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:08.322 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:08.326 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.disconnected", "clientId": "QXL000111222333", "time": "1747032607006"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:08.328 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:08.329 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: QXL000111222333(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:08.454 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:08.590 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:09.003 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:12.427 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:12.741 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:12.789 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:12.814 [main] com.byzp.platform.App:61 - Started App in 29.803 seconds (JVM running for 30.822)
[SW_CTX: N/A] [INFO] 2025-05-12 14:50:40.107 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.disconnected", "clientId": "mqttx_qxl", "time": "1747032640779"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:40.109 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:40.110 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: mqttx_qxl(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:50:40.132 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [INFO] 2025-05-12 14:51:19.062 [MQTT Call: QXL000111222333] com.byzp.platform.callbacks.MqttCallbackConsumer:55 - receiveMessage: topic=server/msg,Qos=1,payload={"msgType": "client.disconnected", "clientId": "mqttx_qxl", "time": "1747032679602"}
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:51:19.065 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==>  Preparing: SELECT id,device_serial,device_name,model,cloud_application_id,cloud_location_group_id,cloud_location_id,create_time FROM cloud_device WHERE (device_serial = ?)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:51:19.066 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - ==> Parameters: mqttx_qxl(String)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:51:19.113 [MQTT Call: QXL000111222333] com.byzp.platform.mapper.CloudDeviceMapper.selectOne:137 - <==      Total: 0
[SW_CTX: N/A] [WARN] 2025-05-12 14:51:37.397 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 14:51:37.441 [SpringApplicationShutdownHook] com.alibaba.druid.pool.DruidDataSource:1863 - {dataSource-1} closed
[SW_CTX: N/A] [INFO] 2025-05-12 14:51:37.442 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
[SW_CTX: N/A] [INFO] 2025-05-12 14:51:52.738 [background-preinit] org.hibernate.validator.internal.util.Version:21 - HV000001: Hibernate Validator 6.2.5.Final
[SW_CTX: N/A] [INFO] 2025-05-12 14:51:53.481 [main] com.byzp.platform.App:55 - Starting App using Java 17 on LAPTOP-0S836JS6 with PID 25464 (D:\cloud-platform\target\classes started by qxl in D:\cloud-platform)
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:51:53.483 [main] com.byzp.platform.App:56 - Running with Spring Boot v2.7.9, Spring v5.3.25
[SW_CTX: N/A] [INFO] 2025-05-12 14:51:53.485 [main] com.byzp.platform.App:637 - The following 1 profile is active: "dev"
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:00.498 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:262 - Multiple Spring Data modules found, entering strict repository configuration mode
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:00.511 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:132 - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:00.610 [main] org.springframework.data.repository.config.RepositoryConfigurationDelegate:201 - Finished Spring Data repository scanning in 47 ms. Found 0 Redis repository interfaces.
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:04.221 [main] com.byzp.kylin.core.KylinCoreAutoConfiguration:52 - Initializing Configuration 'KylinCoreConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:04.253 [main] com.byzp.platform.config.UserArgumentResolver:22 - UserArgumentResolver init......
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:04.255 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'userArgumentResolver' of type [com.byzp.platform.config.UserArgumentResolver] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:04.307 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'validator' of type [org.hibernate.validator.internal.engine.ValidatorImpl] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.029 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#1a7cb3a4' of type [com.dtflys.forest.config.SpringForestProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.040 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#1d6a8386' of type [com.dtflys.forest.reflection.SpringForestObjectFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.091 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean '(inner bean)#7889b4b9' of type [com.dtflys.forest.interceptor.SpringInterceptorFactory] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.239 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'callbackClient' and Proxy of 'com.byzp.platform.client.CallbackClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.239 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceGatewayClient' and Proxy of 'com.byzp.platform.client.ys.DeviceGatewayClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.240 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'deviceMaintainClient' and Proxy of 'com.byzp.platform.client.ys.DeviceMaintainClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.240 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'doorStatusClient' and Proxy of 'com.byzp.platform.client.ys.DoorStatusClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.240 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'fallDeviceClient' and Proxy of 'com.byzp.platform.client.ys.FallDeviceClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.241 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'helpButtonClient' and Proxy of 'com.byzp.platform.client.ys.HelpButtonClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.241 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'humitureClient' and Proxy of 'com.byzp.platform.client.ys.HumitureClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.241 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'sleepSDNL1Client' and Proxy of 'com.byzp.platform.client.ys.SleepSDNL1Client' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.242 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 't1CClient' and Proxy of 'com.byzp.platform.client.ys.T1CClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.242 [main] com.dtflys.forest.scanner.ClassPathClientScanner:158 - [Forest] Created Forest Client Bean with name 'ysClient' and Proxy of 'com.byzp.platform.client.ys.YsClient' client interface
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.303 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'callbackClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.311 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceGatewayClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.317 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'deviceMaintainClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.324 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'doorStatusClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.331 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'fallDeviceClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.338 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'helpButtonClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.347 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'humitureClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.357 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'sleepSDNL1Client' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.365 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 't1CClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:05.377 [main] org.springframework.context.support.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:376 - Bean 'ysClient' of type [com.dtflys.forest.beans.ClientFactoryBean] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:06.622 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:108 - Tomcat initialized with port(s): 8001 (http)
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:06.697 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Initializing ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:06.698 [main] org.apache.catalina.core.StandardService:173 - Starting service [Tomcat]
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:06.698 [main] org.apache.catalina.core.StandardEngine:173 - Starting Servlet engine: [Apache Tomcat/9.0.71]
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:07.172 [main] org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]:173 - Initializing Spring embedded WebApplicationContext
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:07.172 [main] org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext:292 - Root WebApplicationContext: initialization completed in 12552 ms
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:07.643 [main] com.byzp.kylin.data.KylinDBConfiguration:33 - Initializing Configuration 'KylinDBConfiguration'
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:07.721 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:25 - Async executor for mybatis initialized.
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:07.775 [main] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:45 - MybatisConfiguration be replaced...
[SW_CTX: N/A] [WARN] 2025-05-12 14:52:13.158 [main] com.baomidou.mybatisplus.core.metadata.TableInfoHelper:327 - Can not find table primary key in Class: "com.byzp.platform.mapper.po.CloudVoiceInfo".
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:13.500 [main] com.anji.captcha.config.AjCaptchaServiceAutoConfiguration:33 - 自定义配置项：
AjCaptchaProperties{type=DEFAULT, jigsaw='', picClick='', waterMark='我的水印', waterFont='WenQuanZhengHei.ttf', fontType='WenQuanZhengHei.ttf', slipOffset='5', aesStatus=true, interferenceOptions='0', cacheNumber='1000', timingClear='180', cacheType=local, reqFrequencyLimitEnable=false, reqGetLockLimit=5, reqGetLockSeconds=300, reqGetMinuteLimit=100, reqCheckMinuteLimit=100, reqVerifyMinuteLimit=100}
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:13.510 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:52 - supported-captchaCache-service:[local]
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:13.519 [main] com.anji.captcha.service.impl.CaptchaServiceFactory:58 - supported-captchaTypes-service:[clickWord, default, blockPuzzle]
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:13.558 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@5f0ca069, ORIGINAL=[Ljava.lang.String;@6a6a2fdd, PIC_CLICK=[Ljava.lang.String;@552ffa44]
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:13.559 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---clickWord
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:13.806 [main] com.anji.captcha.service.impl.ClickWordCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:14.066 [main] com.anji.captcha.util.ImageUtils:48 - 初始化底图:[SLIDING_BLOCK=[Ljava.lang.String;@69944a90, ORIGINAL=[Ljava.lang.String;@1ed52f44, PIC_CLICK=[Ljava.lang.String;@771afdd5]
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:14.068 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:76 - --->>>初始化验证码底图<<<---blockPuzzle
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:14.213 [main] com.anji.captcha.service.impl.BlockPuzzleCaptchaServiceImpl:92 - 初始化local缓存...
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:52:17.743 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerAdapter:625 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[SW_CTX: N/A] [DEBUG] 2025-05-12 14:52:18.371 [main] com.byzp.kylin.core.extension.KylinRequestMappingHandlerMapping:367 - 265 mappings in 'requestMappingHandlerMapping'
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:18.743 [main] com.byzp.kylin.core.extension.KylinExceptionResolver:32 - KylinExceptionResolver is instantiated.
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:22.429 [main] org.springframework.boot.actuate.endpoint.web.EndpointLinksResolver:58 - Exposing 12 endpoint(s) beneath base path '/actuator'
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:22.635 [main] org.apache.coyote.http11.Http11NioProtocol:173 - Starting ProtocolHandler ["http-nio-8001"]
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:22.867 [main] org.springframework.boot.web.embedded.tomcat.TomcatWebServer:220 - Tomcat started on port(s): 8001 (http) with context path ''
[SW_CTX: N/A] [INFO] 2025-05-12 14:52:22.902 [main] com.byzp.platform.App:61 - Started App in 31.866 seconds (JVM running for 33.921)
[SW_CTX: N/A] [WARN] 2025-05-12 14:58:50.501 [SpringApplicationShutdownHook] org.springframework.beans.factory.support.DisposableBeanAdapter:248 - Invocation of close method failed on bean with name 'initMqttClient': 已连接客户机 (32100)
[SW_CTX: N/A] [INFO] 2025-05-12 14:58:50.521 [SpringApplicationShutdownHook] com.byzp.kylin.data.extension.KylinMybatisPlusPropertiesCustomizer:34 - Async executor for mybatis destroyed.
