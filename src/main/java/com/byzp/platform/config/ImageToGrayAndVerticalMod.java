package com.byzp.platform.config;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;

public class ImageToGrayAndVerticalMod {
//    public static void main(String[] args) {
//        try {
//            // 读取图片
//            File input = new File("D:\\audio\\tt2.png");
//            BufferedImage image = ImageIO.read(input);
//
//            // 转换为灰度图
//            BufferedImage grayImage = convertToGrayScale(image);
//
//            // 垂直扫描取模
//            byte[] verticalMod = verticalScanMod(grayImage);
//            System.out.println("取模数据===========");
//            System.out.println(verticalMod.length);
//            // 打印取模结果（示例）
//            for (int col = 0; col < verticalMod.length; col++) {
////                for (int row = 0; row < verticalMod[col].length; row++) {
//                    System.out.print(String.format(",0x%02X ", verticalMod[col]));
////                }
////                System.out.println();
//            }
//
//            // 保存灰度图
//            File output = new File("D:\\audio\\output_gray2.png");
//            ImageIO.write(grayImage, "png", output);
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }

    // 转换为灰度图
    public static BufferedImage convertToGrayScale(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        BufferedImage grayImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int argb = image.getRGB(x, y);
                int alpha = (argb >> 24) & 0xff;
                int red = (argb >> 16) & 0xff;
                int green = (argb >> 8) & 0xff;
                int blue = argb & 0xff;

                // 计算灰度值
                int gray = (int) (0.299 * red + 0.587 * green + 0.114 * blue);

                // 设置灰度图的像素值
                int grayArgb = (alpha << 24) | (gray << 16) | (gray << 8) | gray;
                grayImage.setRGB(x, y, grayArgb);
            }
        }
        return grayImage;
    }

    // 垂直扫描取模
    public static byte[] verticalScanMod(BufferedImage image) {
        int width = image.getWidth();
        int height = image.getHeight();
        int byteHeight = (height + 7) / 8;
        byte[] modData = new byte[width*byteHeight];

        for (int x = 0; x < width; x++) {
            for (int y = 0; y < height; y++) {
                int pixel = image.getRGB(x, y) & 0xff;
                int bitIndex = y % 8;
                int byteIndex = y / 8;
                if (pixel < 128) {
                    modData[x*byteHeight+byteIndex] |= (1 << bitIndex);
                }
            }
        }
        return modData;
    }
}