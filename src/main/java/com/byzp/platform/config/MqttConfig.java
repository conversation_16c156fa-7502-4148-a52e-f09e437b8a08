package com.byzp.platform.config;

import com.alibaba.druid.sql.visitor.functions.Char;
import com.byzp.platform.callbacks.MqttCallbackConsumer;
import com.byzp.platform.service.CloudApplicationServiceImpl;
import com.byzp.platform.utils.SSLUtils;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.awt.*;
import java.io.*;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Component
@Slf4j
public class MqttConfig {

    @Value("${mqtt.caPath}")
    private String mqttCaPath;
    @Resource
    private CloudApplicationServiceImpl cloudApplicationService;
    @Value("${audio.filePath}")
    private String mediaPath;
    @Value("${mqtt.topic}")
    private String topic;
    @Value("${mqtt.qos}")
    private int qos;
    @Value("${mqtt.maxRetryAttempts}")
    private int maxRetryAttempts;
    @Value("${mqtt.initialRetryDelay}")
    private long initialRetryDelay;

    @Bean
    public MqttClient initMqttClient() {
        try {
            String broker = "ssl://w6189116.ala.cn-hangzhou.emqxsl.cn:8883";
            String clientId = MqttClient.generateClientId();
            MemoryPersistence persistence = new MemoryPersistence();
            MqttConnectOptions connOpts = new MqttConnectOptions();
            connOpts.setCleanSession(true);
            connOpts.setUserName("qxlTest");
            connOpts.setPassword("123456".toCharArray());
            connOpts.setAutomaticReconnect(true);
            // 设置CA证书
            try {
                connOpts.setSocketFactory(SSLUtils.getSingleSocketFactory(mqttCaPath));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            MqttClient client = new MqttClient(broker, clientId, persistence);

            // 设置回调
            client.setCallback(new MqttCallbackConsumer(cloudApplicationService, mediaPath, client, topic, qos, maxRetryAttempts, initialRetryDelay));
            // 建立连接
            log.info("Connecting to broker start: " + broker);
            client.connect(connOpts);
            log.info("Connected to broker success: " + broker);
            // 订阅 topic
            client.subscribe(topic, qos);
            return client;
        } catch (MqttException me) {
            System.out.println("reason " + me.getReasonCode());
            System.out.println("msg " + me.getMessage());
            System.out.println("loc " + me.getLocalizedMessage());
            System.out.println("cause " + me.getCause());
            System.out.println("excep " + me);
            me.printStackTrace();
        }
        return null;
    }

//    public static void main(String[] args) throws Exception {
//        Map<Integer, Long> indexMap = new HashMap<>();
//        try (BufferedReader br = new BufferedReader(new FileReader("D:\\fontModel\\FontLibrary.bin_index.txt"))) {
//            String line;
//            int index = 0;
//            while ((line = br.readLine()) != null) {
//                char[] parts = line.toCharArray();
//                for (int i = 0; i < parts.length; i++) {
////                    if (!Character.isISOControl(parts[i])) {
//                        indexMap.put((int) parts[i], (long) index++);
////                    }
//                }
//            }
//        }
////        for (Integer index : indexMap.keySet()) {
////            System.out.println(index + "," + indexMap.get(index));
////        }
//
//        char c = '!';
////        for (int m = 0; m <10; m++){
////            System.out.println("index m=" + m);
//            byte[] bytes = readFontData(new RandomAccessFile("D:\\fontModel\\FontLibrary.bin","r"), indexMap.get((int)c), 48);
//            for (int i = 0; i < bytes.length; i++) {
//                System.out.printf(",0x%02X ", bytes[i]);
//            }
//            System.out.println();
////        }
//
//    }

//    public static void main(String[] args) throws Exception {
//        Map<Long, Long> indexMap = new HashMap<>();
//        try (BufferedReader br = new BufferedReader(new FileReader("D:\\cloud-platform\\src\\main\\resources\\text_index.txt"))) {
//            String line;
//            int index = 0;
//            while ((line = br.readLine()) != null) {
//                char[] parts = line.toCharArray();
//                if (parts.length > 10) {
//                    for (int i = 0; i < parts.length; i++) {
//                        indexMap.put((long) parts[i], (long) index++);
//                    }
//                }
//            }
//        }
////        for (Long index : indexMap.keySet()) {
////            System.out.println(index + "," + indexMap.get(index));
////        }
//        String name = "长者";
//
//        byte[] bytes = new byte[name.length()*32];
//        for (int i = 0; i < name.length(); i++) {
//            char c = name.charAt(i);
//            byte[] part = readFontData(new RandomAccessFile("D:\\cloud-platform\\src\\main\\resources\\text16.fon","r"), indexMap.get((long)c), 32);
//            System.arraycopy(part, 0, bytes, i*32, 32);
//        }
////        char c = '_';
////        byte[] bytes = readFontData(new RandomAccessFile("D:\\fontModel\\FontLibrary24.bin","r"), indexMap.get((int)c), 32);
////        System.out.println("长度：" + bytes.length);
//
//        for (int j = 0; j < bytes.length; j++) {
//            System.out.print(String.format(",0x%02X ", bytes[j]));
//        }
//        System.out.println();
//        System.out.println("bytes length:" + bytes.length);
//        System.out.println("base64:" + Base64.getEncoder().encodeToString(bytes));
//
//    }

    public static byte[] readFontData(RandomAccessFile binFile, long offset, int charSize)
            throws IOException {
        System.out.println("offset:" + offset);
        binFile.seek(offset*charSize);
        byte[] buffer = new byte[charSize];
        binFile.readFully(buffer);
        return buffer;
    }

    public static void drawCharacter(Graphics2D g, byte[] fontData, int x, int y, int pixelSize) {
        int bytesPerRow = fontData.length / 16; // 16x16字体示例

        for (int row = 0; row < 16; row++) {
            for (int col = 0; col < bytesPerRow; col++) {
                byte b = fontData[row * bytesPerRow + col];
                // 处理逆向取模（根据软件设置调整bit遍历顺序）:ml-citation{ref="4" data="citationList"}
                for (int bit = 7; bit >= 0; bit--) {
                    if (((b >> bit) & 0x01) == 1) {
                        int px = x + (col * 8 + (7 - bit)) * pixelSize;
                        int py = y + row * pixelSize;
                        g.fillRect(px, py, pixelSize, pixelSize);
                    }
                }
            }
        }
    }
}
