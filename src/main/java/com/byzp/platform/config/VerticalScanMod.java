//package com.byzp.platform.config;
//
//
//import org.opencv.core.Core;
//import org.opencv.core.CvType;
//import org.opencv.core.Mat;
//import org.opencv.imgcodecs.Imgcodecs;
//import org.opencv.imgproc.Imgproc;
//
//public class VerticalScanMod {
//    static {
//        nu.pattern.OpenCV.loadLocally();
//    }
//
//    public static void main(String[] args) {
//        // 1. 读取图像并转为灰度图
//        Mat src = Imgcodecs.imread("D:\\audio\\tt2.png");
//        Mat gray = new Mat();
//        Imgproc.cvtColor(src, gray, Imgproc.COLOR_BGR2GRAY);
//
//        // 2. 二值化处理
//        Mat binary = new Mat();
//        Imgproc.threshold(gray, binary, 128, 255, Imgproc.THRESH_BINARY);
//
//        // 3. 垂直扫描生成字节数组
//        byte[] modData = verticalScan(binary);
//
//        // 4. 输出结果（示例：C语言数组格式）
//        System.out.println("// 垂直取模数据:" + modData.length);
//        System.out.print("const uint8_t MOD_DATA[] = {");
//        for (int i = 0; i < modData.length; i++) {
//            if (i % 16 == 0) System.out.print("\n    ");
//            System.out.printf("0x%02X", (modData[i] & 0xFF));
//            if (i < modData.length - 1) System.out.print(", ");
//        }
//        System.out.println("\n};");
//    }
//
//    private static byte[] verticalScan(Mat binaryImg) {
//        int width = binaryImg.cols();
//        int height = binaryImg.rows();
//        int bytesPerColumn = (int) Math.ceil(height / 8.0);
//        byte[] result = new byte[width * bytesPerColumn];
//
//        for (int col = 0; col < width; col++) {
//            for (int byteNum = 0; byteNum < bytesPerColumn; byteNum++) {
//                int byteValue = 0;
//                int startRow = byteNum * 8;
//                for (int bit = 0; bit < 8; bit++) {
//                    int row = startRow + bit;
//                    if (row < height) {
//                        double[] pixel = binaryImg.get(row, col);
//                        if (pixel[0] > 128) { // 白色像素为1
//                            byteValue |= (1 << (7 - bit)); // 高位在前
//                        }
//                    }
//                }
//                result[col * bytesPerColumn + byteNum] = (byte) byteValue;
//            }
//        }
//        return result;
//    }
//}