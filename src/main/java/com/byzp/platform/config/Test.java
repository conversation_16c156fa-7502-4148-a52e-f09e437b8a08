package com.byzp.platform.config;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.utils.AudioConverter;
import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class Test {
    public static void main(String[] args) throws Exception {
        JSONObject object = new JSONObject();
        object.put("backgroundColor", "WHITE");
        object.put("width", 416);
        object.put("height", 240);
        JSONArray elements = new JSONArray();
        JSONObject e1 = new JSONObject();
        e1.put("type", "label");
        e1.put("value","长者姓名");
        e1.put("size", 16);
        e1.put("x", 10);
        e1.put("y",10);
        e1.put("z", 0);
        elements.add(e1);
        JSONObject e2 = new JSONObject();
        e2.put("type", "label");
        e2.put("field","name");
        e2.put("size", 16);
        e2.put("x", 82);
        e2.put("y",10);
        e2.put("z", 1);
        elements.add(e2);
        JSONObject e3 = new JSONObject();
        e3.put("type", "label");
        e3.put("value","床位号");
        e3.put("size", 16);
        e3.put("x", 10);
        e3.put("y",40);
        e3.put("z", 2);
        elements.add(e3);
        JSONObject e4 = new JSONObject();
        e4.put("type", "label");
        e4.put("field","age");
        e4.put("size", 16);
        e4.put("x", 82);
        e4.put("y",40);
        e4.put("z", 3);
        elements.add(e4);
        JSONObject e5 = new JSONObject();
        e5.put("type", "label");
        e5.put("value","失能情况");
        e5.put("size", 16);
        e5.put("x", 10);
        e5.put("y",70);
        e5.put("z", 4);
        elements.add(e5);
        JSONObject e6 = new JSONObject();
        e6.put("type", "label");
        e6.put("field","status");
        e6.put("size", 16);
        e6.put("x", 82);
        e6.put("y",70);
        e6.put("z", 5);
        elements.add(e6);
//        File imageFile = new File("D:\\audio\\pic384.png");
//        String base64 = compressAndConvertToBase64(imageFile);
        JSONObject e7 = new JSONObject();
        e7.put("type", "qrcode");
        e7.put("value","https://www.pin-bike.com/");
        e7.put("size", 80);
        e7.put("x", 200);
        e7.put("y", 40);
        e7.put("z", 7);
        elements.add(e7);
//        JSONObject e8 = new JSONObject();
//        e8.put("type", "qrcode");
//        e8.put("field", "qrcode");
//        e8.put("size", 64);
//        e8.put("x", 200);
//        e8.put("y", 40);
//        e8.put("z", 7);
//        elements.add(e8);
        object.put("elements", elements);
        System.out.println(object.toJSONString());
        String aa = "[{\"field\":\"name\",\"value\":\"徐金林\"},{\"field\":\"age\",\"value\":\"3104\"},{\"field\":\"status\",\"value\":\"轻度失能\"}]";
        JSONArray data = new JSONArray();
        JSONObject d1 = new JSONObject();
        d1.put("field", "name");
        d1.put("value", "张大伟");
        data.add(d1);
        JSONObject d2 = new JSONObject();
        d2.put("field", "age");
        d2.put("value", "77");
        data.add(d2);
        JSONObject d3 = new JSONObject();
        d3.put("field", "status");
        d3.put("value", "轻度失能");
        data.add(d3);
        System.out.println(data);
////        BufferedImage image = ImageIO.read(new File("E:\\360MoveData\\Users\\byzp-\\Desktop\\aaaa.bmp"));
////        BufferedImage image = ImageIO.read(new File("D:\\audio\\tt3.png"));
//        BufferedImage image = generateQRCodeImage("https://www.baidu.com",64,64);
//        image = convertToGrayScale(image);
//        image = convertToBinary(image, 190);
////        image = adjustBrightness(image, 80);
////        image = gammaCorrection(image, 3);
//        byte[] bytes = verticalScan(image);
//        System.out.println("取模结果:" + bytes.length);
//        for (int i = 0; i < bytes.length; i++) {
//            System.out.print(String.format(",0x%02X ", bytes[i]));
//        }
//        System.out.println();
//        System.out.println("base64:" + Base64.getEncoder().encodeToString(bytes));
//        ImageIO.write(image, "png", new File("D:\\audio\\baidu.png"));
//
//
////        NetConfig              config  = new NetConfig();
////        DefaultReceiverContext context = new DefaultReceiverContext();
////        context.setProperty("test", "123");
////        context.addReference(new Main());
////        context.addReference(new RandomTaskDispatcher());
////        config.setContext(context);
////        var service = new DefaultPacketService(config);
////        System.out.println(service);

//        File imageFile2 = new File("D:\\audio\\pic64gray.png");
//        String base642 = compressAndConvertToBase64(imageFile);
//        System.out.println("Base64 String:\n" + base642);
//        File audioData = new File("C:\\Users\\<USER>\\Desktop\\11.txt");
//        String s = Files.readString(Path.of("C:\\Users\\<USER>\\Desktop\\11.txt"));
//        String mp3Path = "D:\\audio1\\" + UUID.randomUUID().toString().replaceAll("-", "") + ".mp3";
//        AudioConverter.convertPcmBase64ToMp3File(s, mp3Path);
    }

    public static BufferedImage generateQRCodeImage(String content, int width, int height) throws Exception {
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        BitMatrix matrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
        return MatrixToImageWriter.toBufferedImage(matrix);
    }

    public static String compressAndConvertToBase64(File inputImageFile) throws IOException {
        // 1. 读取原始图片
        BufferedImage originalImage = ImageIO.read(inputImageFile);

        // 2. 创建 64x64 的缩略图
        int width = 64;
        int height = 64;
        BufferedImage resizedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = resizedImage.createGraphics();
        g.drawImage(originalImage, 0, 0, width, height, null);
        g.dispose();

        // 3. 写入到 ByteArrayOutputStream 中
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ImageIO.write(resizedImage, "png", baos);
        byte[] imageBytes = baos.toByteArray();

        // 4. 转成 Base64 字符串
        return Base64.getEncoder().encodeToString(imageBytes);
    }

    // 1. 灰度化
    public static BufferedImage convertToGrayScale(BufferedImage srcImage) {
        int           width     = srcImage.getWidth();
        int           height    = srcImage.getHeight();
        BufferedImage grayImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = srcImage.getRGB(x, y);
                int r   = (rgb >> 16) & 0xFF;
                int g   = (rgb >> 8) & 0xFF;
                int b   = rgb & 0xFF;
                // 加权灰度公式（ITU-R BT.601标准）
                int grayValue = (int) (0.299 * r + 0.587 * g + 0.114 * b);
                int grayPixel = (grayValue << 16) | (grayValue << 8) | grayValue;
                grayImage.setRGB(x, y, grayPixel);
            }
        }
        return grayImage;
    }

    // 2. 二值化（需指定阈值）
    public static BufferedImage convertToBinary(BufferedImage grayImage, int threshold) {
        int           width       = grayImage.getWidth();
        int           height      = grayImage.getHeight();
        BufferedImage binaryImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb         = grayImage.getRGB(x, y);
                int grayValue   = (rgb >> 16) & 0xFF; // 提取灰度值
                int binaryValue = (grayValue > threshold) ? 0xFFFFFF : 0x000000; // 白或黑
                binaryImage.setRGB(x, y, binaryValue);
            }
        }
        return binaryImage;
    }

    // 3. 调整亮度（delta范围：-255到255）
    public static BufferedImage adjustBrightness(BufferedImage srcImage, int delta) {
        int           width       = srcImage.getWidth();
        int           height      = srcImage.getHeight();
        BufferedImage brightImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb    = srcImage.getRGB(x, y);
                int r      = Math.max(0, Math.min(255, ((rgb >> 16) & 0xFF) + delta));
                int g      = Math.max(0, Math.min(255, ((rgb >> 8) & 0xFF) + delta));
                int b      = Math.max(0, Math.min(255, (rgb & 0xFF) + delta));
                int newRGB = (r << 16) | (g << 8) | b;
                brightImage.setRGB(x, y, newRGB);
            }
        }
        return brightImage;
    }

    public static int calculateOtsuThreshold(BufferedImage grayImage) {
        int[] histogram   = new int[256];
        int   totalPixels = grayImage.getWidth() * grayImage.getHeight();

        // 统计灰度直方图
        for (int y = 0; y < grayImage.getHeight(); y++) {
            for (int x = 0; x < grayImage.getWidth(); x++) {
                int grayValue = (grayImage.getRGB(x, y) >> 16) & 0xFF;
                histogram[grayValue]++;
            }
        }

        // 计算最大类间方差对应的阈值
        float sum    = 0, sumB = 0;
        int   wB     = 0, wF, threshold = 0;
        float varMax = 0;

        for (int i = 0; i < 256; i++) sum += i * histogram[i];

        for (int t = 0; t < 256; t++) {
            wB += histogram[t];
            if (wB == 0) continue;
            wF = totalPixels - wB;
            if (wF == 0) break;

            sumB += t * histogram[t];
            float mB         = sumB / wB;
            float mF         = (sum - sumB) / wF;
            float varBetween = (float) wB * wF * (mB - mF) * (mB - mF);

            if (varBetween > varMax) {
                varMax = varBetween;
                threshold = t;
            }
        }
        return threshold;
    }

    // 使用伽马校正（Gamma Correction）实现更自然的亮度调节
    public static BufferedImage gammaCorrection(BufferedImage srcImage, float gamma) {
        BufferedImage correctedImage = new BufferedImage(srcImage.getWidth(), srcImage.getHeight(), BufferedImage.TYPE_INT_RGB);
        for (int y = 0; y < srcImage.getHeight(); y++) {
            for (int x = 0; x < srcImage.getWidth(); x++) {
                int rgb    = srcImage.getRGB(x, y);
                int r      = (int) (255 * Math.pow(((rgb >> 16) & 0xFF) / 255.0, gamma));
                int g      = (int) (255 * Math.pow(((rgb >> 8) & 0xFF) / 255.0, gamma));
                int b      = (int) (255 * Math.pow((rgb & 0xFF) / 255.0, gamma));
                int newRGB = (r << 16) | (g << 8) | b;
                correctedImage.setRGB(x, y, newRGB);
            }
        }
        return correctedImage;
    }

    private static byte[] verticalScan(BufferedImage binaryImg) {
        int width = binaryImg.getWidth();
        int height = binaryImg.getHeight();
        int bytesPerColumn = (int) Math.ceil(height / 8.0);
        byte[] result = new byte[width * bytesPerColumn];

        for (int col = 0; col < width; col++) {
            for (int byteNum = 0; byteNum < bytesPerColumn; byteNum++) {
                int byteValue = 0;
                int startRow = byteNum * 8;
                for (int bit = 0; bit < 8; bit++) {
                    int row = startRow + bit;
                    if (row < height) {
//                        double[] pixel = binaryImg.get(row, col);
                        int pixel = binaryImg.getRGB(col, row) & 0xff;
                        if (pixel > 128) { // 白色像素为1
                            byteValue |= (1 << (7 - bit)); // 高位在前
                        }
                    }
                }
                result[col * bytesPerColumn + byteNum] = (byte) byteValue;
            }
        }
        return result;
    }
}
