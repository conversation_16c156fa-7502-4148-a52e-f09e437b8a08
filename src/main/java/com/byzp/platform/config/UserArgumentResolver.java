package com.byzp.platform.config;

import com.auth0.jwt.JWT;
import com.byzp.platform.dto.CloudUserDTO;
import com.byzp.kylin.core.extension.points.KylinHandlerMethodArgumentResolver;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;

@Slf4j
@Component
public class UserArgumentResolver implements KylinHandlerMethodArgumentResolver {

    public UserArgumentResolver(){
        log.info("UserArgumentResolver init......");
    }
    @Override
    public boolean supportsParameter(MethodParameter parameter) {
//        return UserDTO.class.isAssignableFrom(parameter.getParameterType());
        return true;
    }

    private String findToken(HttpServletRequest request) {
        String jwt = request.getHeader("token");
        if (jwt == null) {
            jwt = request.getParameter("token");
        }
        return jwt;
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        HttpServletRequest httpServletRequest = (HttpServletRequest) webRequest.getNativeRequest();
        String token = findToken(httpServletRequest);
        if (StringUtils.isNotBlank(token)) {
            val jwt = JWT.decode(token);
            CloudUserDTO userDTO = new CloudUserDTO();
            userDTO.setId(Long.valueOf(jwt.getIssuer()));
            userDTO.setAccount(jwt.getSubject());
            return userDTO;
        } else {
            log.info("cannot found key for API: {}", parameter.getMethod().getName());
            return null;
        }
    }
}
