package com.byzp.platform.param;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class CloudDeviceParam implements Serializable {
    @Serial
    private static final long serialVersionUID = -6248651714270759535L;


    private Long id;
    /**
     * 设备序列号
     */
    private String deviceSerial;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 型号
     */
    private String model;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 分组id
     */
    private Long cloudLocationGroupId;
    /**
     * 位置id
     */
    private Long cloudLocationId;

    private Integer pageNo;

    private Integer pageSize;

    private String createTimeStart;

    private String createTimeEnd;

    private Long cloudUserId;
    /**
     * 设备验证码
     */
    private String validateCode;
    /**
     * 萤石--睡眠伴侣配置
     */
    private String startTime;
    /**
     * 萤石--睡眠伴侣配置
     */
    private String endTime;
    /**
     * 萤石--睡眠伴侣配置
     */
    private Integer outBedTime;
    /**
     * 关联的设备序列号（添加智能尿不湿时，这个序列号是睡眠雷达的序列号）
     */
    private String relDeviceSerial;
}
