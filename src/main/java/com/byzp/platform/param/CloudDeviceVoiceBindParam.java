package com.byzp.platform.param;

import lombok.Data;
import org.apache.catalina.LifecycleState;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CloudDeviceVoiceBindParam implements Serializable {
    @Serial
    private static final long serialVersionUID = -2539891579374705757L;
    private Long id;
    /**
     * 设备序列号
     */
    private String deviceSerial;
    /**
     * 音频id
     */
    private String voiceId;
    /**
     * 音频base64
     */
    private String voiceBase64;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 创建时间开始
     */
    private String createTimeStart;
    /**
     * 创建时间结束
     */
    private String createTimeEnd;
    /**
     * 声纹id列表
     */
    private List<String> voiceIdList;
    /**
     * 绑定关系列表
     */
    private List<CloudDeviceVoiceBindParam> bindInfoList;

}
