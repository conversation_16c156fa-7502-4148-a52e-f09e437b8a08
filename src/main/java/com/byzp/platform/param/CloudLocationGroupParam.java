package com.byzp.platform.param;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class CloudLocationGroupParam implements Serializable {
    @Serial
    private static final long serialVersionUID = -4248053012631255131L;

    private Long id;
    /**
     * 位置id
     */
    private Long locationId;
    /**
     * 分组名称
     */
    private String name;
    /**
     * 备注
     */
    private String remark;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 应用id
     */
    private Long cloudApplicationId;

    private Integer pageNo;

    private Integer pageSize;

    private String createTimeStart;

    private String createTimeEnd;

}
