package com.byzp.platform.param;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudVoiceInfoParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 787996134780168875L;

    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 声纹id（同一个应用下唯一）
     */
    private String voiceId;
    /**
     * 声纹名称
     */
    private String voiceName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 声纹base64
     */
    private String voiceBase64;

    private Date createTime;

    private Integer pageNo;

    private Integer pageSize;

    private String createTimeStart;

    private String createTimeEnd;
}
