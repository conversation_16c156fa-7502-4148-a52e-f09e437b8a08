package com.byzp.platform.param;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class DeviceVarietyParam implements Serializable {

    @Serial
    private static final long serialVersionUID = -2880601187341065177L;

    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 型号
     */
    private String model;
    /**
     * 图片链接
     */
    private String picUrl;

    private Integer linkType;

    private Integer type;

    private Integer isDelete;

    private Date createTime;

    private Integer pageNo;

    private Integer pageSize;
}
