package com.byzp.platform.param;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class SmldTimeParam implements Serializable {
    @Serial
    private static final long serialVersionUID = -8765405627543113268L;

    /**
     * 应用id
     */
    private Long cloudApplicationId;

    /**
     * 设备序列号
     */
    private String deviceSerial;
    /**
     * 设备型号
     */
    private String deviceModel;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
}
