package com.byzp.platform.param;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class CloudLocationParam implements Serializable {
    @Serial
    private static final long serialVersionUID = -4189925257999363848L;

    private Long id;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 名称
     */
    private String name;
    /**
     * 备注
     */
    private String remark;
    /**
     * 用户id
     */
    private Long userId;

    private Long cloudUserId;

    private Integer pageNo;

    private Integer pageSize;

    private String createTimeStart;

    private String createTimeEnd;
}
