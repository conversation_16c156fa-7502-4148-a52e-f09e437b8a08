package com.byzp.platform.param;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class SmldWarnParam implements Serializable {
    @Serial
    private static final long serialVersionUID = 2585437208448343342L;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 设备序列号
     */
    private String deviceSerial;
    /**
     * 设备型号
     */
    private String deviceModel;
    /**
     * 离床时长
     */
    private Integer LcWarnTime;
    /**
     * 呼吸最大值
     */
    private Integer hxWarnMax;
    /**
     * 呼吸最小值
     */
    private Integer hxWarnMin;
    /**
     * 心率最大值
     */
    private Integer xlWarnMax;
    /**
     * 心率最小值
     */
    private Integer xlWarnMin;
}
