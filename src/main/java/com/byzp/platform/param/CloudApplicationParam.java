package com.byzp.platform.param;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CloudApplicationParam implements Serializable {
    @Serial
    private static final long serialVersionUID = -693740157635656931L;
    private Long id;
    /**
     * 云平台账号id
     */
    private Long cloudUserId;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 应用备注信息
     */
    private String remark;
    /**
     * 第三方通知回调接口
     */
    private String noticeCallbackUrl;
    /**
     * 声纹相似度，0-1，数值越大相似度越高，默认0.6
     */
    private BigDecimal voiceSimilarity;

    private Integer pageNo;

    private Integer pageSize;
}
