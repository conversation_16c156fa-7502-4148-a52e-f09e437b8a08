package com.byzp.platform.param;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class CloudUserParam implements Serializable {
    @Serial
    private static final long serialVersionUID = -7452053577080578564L;

    private Long id;
    /**
     * 账号（手机号）
     */
    private String account;
    /**
     * 密码
     */
    private String password;
    /**
     * 验证码
     */
    private String validCode;
}
