package com.byzp.platform.filters;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byzp.platform.mapper.CloudApplicationMapper;
import com.byzp.platform.mapper.po.CloudApplication;
import com.byzp.platform.utils.RedisUtils;
import com.volcengine.service.visual.IVisualService;
import com.volcengine.service.visual.impl.VisualServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StreamUtils;

import javax.annotation.Resource;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Set;

@Slf4j
@Component
public class AuthFilter implements Filter {

    // 设置白名单路径，可以配置为 yml 配置或常量
    private static final Set<String> WHITELIST = Set.of(
            "/api/device/**",
            "/api/fallDevice/**",
            "/api/gatewayDevice/**",
            "/api/helpButton/**",
            "/api/humiture/**",
            "/api/maintain/**",
            "/api/doorStatus/**",
            "/api/sleepSDNL1/**",
            "/api/t1c/**",
            "/api/cloudDevice/**"
    );

    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Resource
    private CloudApplicationMapper cloudApplicationMapper;
    @Resource
    private RedisUtils redisUtils;

    private static final String HMAC_SHA256_ALGORITHM = "HmacSHA256";

    /**
     * 计算HMAC-SHA256签名
     */
    private String calculateHMAC(String data, String key) {
        try {
            SecretKeySpec signingKey = new SecretKeySpec(key.getBytes(StandardCharsets.UTF_8), HMAC_SHA256_ALGORITHM);
            Mac mac = Mac.getInstance(HMAC_SHA256_ALGORITHM);
            mac.init(signingKey);
            byte[] rawHmac = mac.doFinal(data.getBytes(StandardCharsets.UTF_8));
            return bytesToHex(rawHmac);
        } catch (Exception e) {
            log.error("计算HMAC签名失败:", e);
            throw new RuntimeException("计算签名时发生错误", e);
        }
    }

    /**
     * 将字节数组转换为十六进制字符串
     */
    private String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) {
                hexString.append('0');
            }
            hexString.append(hex);
        }
        return hexString.toString();
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest httpServletRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpServletResponse = (HttpServletResponse) servletResponse;

        try {
            log.info("HTTP Request Method: {}", httpServletRequest.getMethod());
            log.info("HTTP Request URI: {}", httpServletRequest.getRequestURI());
            log.info("HTTP Request Headers: {}", httpServletRequest.getHeaderNames());
            String path = httpServletRequest.getRequestURI();
            // 1. 判断是否匹配白名单，直接放行
            boolean isWhitelisted = WHITELIST.stream()
                    .anyMatch(pattern -> pathMatcher.match(pattern, path));
            if (isWhitelisted) {
                filterChain.doFilter(servletRequest, servletResponse);
                return;
            }
            // 2. IP黑名单校验
            String clientIp = getClientIp(httpServletRequest);
            if (redisUtils.hasKey("api_blacklist:" + clientIp)) {
                log.warn("IP黑名单拦截, IP: {}", clientIp);
                sendError(httpServletResponse, HttpServletResponse.SC_FORBIDDEN, "您的IP已被禁止访问");
                return;
            }
            // 3. 签名参数校验
            String sign = httpServletRequest.getHeader("sign");
            if (StringUtils.isBlank(sign)) {
                log.warn("签名验证失败: 缺少签名参数, URI: {}", path);
                sendError(httpServletResponse, HttpServletResponse.SC_UNAUTHORIZED, "认证失败，缺少签名");
                return;
            }
            String appId = httpServletRequest.getHeader("X-App-Id");
            String timestamp = httpServletRequest.getHeader("timestamp");
            if (StringUtils.isBlank(timestamp)) {
                log.warn("签名验证失败: 缺少时间戳参数, URI: {}, AppId: {}", path, appId);
                sendError(httpServletResponse, HttpServletResponse.SC_UNAUTHORIZED, "认证失败,缺少时间");
                return;
            }
            // 4. 时间戳防重放校验（5分钟）
            long now = System.currentTimeMillis();
            long ts = Long.parseLong(timestamp);
            if (Math.abs(now - ts) > 5 * 60 * 1000) {
                log.warn("签名验证失败: 请求已过期, URI: {}, AppId: {}, Timestamp: {}", path, appId, timestamp);
                sendError(httpServletResponse, HttpServletResponse.SC_REQUEST_TIMEOUT, "请求已过期");
                return;
            }
            if (StringUtils.isBlank(appId)) {
                log.warn("签名验证失败: 缺少AppId参数, URI: {}", path);
                sendError(httpServletResponse, HttpServletResponse.SC_UNAUTHORIZED, "认证失败，缺少凭证");
                return;
            }
            // 5. 查询AppId对应密钥
            CloudApplication cloudApplication = cloudApplicationMapper.selectOne(new QueryWrapper<CloudApplication>()
                    .lambda().eq(CloudApplication::getAppId, appId));
            if (cloudApplication == null) {
                log.warn("签名验证失败: 无效的AppId, URI: {}, AppId: {}", path, appId);
                sendError(httpServletResponse, HttpServletResponse.SC_UNAUTHORIZED, "认证失败：无效的应用id或应用已禁用");
                return;
            }
            // 6. 组装签名内容（GET用queryString，POST/PUT用body）
            String method = httpServletRequest.getMethod();
            String paramStr;
            if ("GET".equalsIgnoreCase(method)) {
                paramStr = StringUtils.defaultString(httpServletRequest.getQueryString(), "");
            } else {
                paramStr = StreamUtils.copyToString(httpServletRequest.getInputStream(), StandardCharsets.UTF_8);
                paramStr = StringUtils.defaultString(paramStr, "");
            }
            // 7. 验证签名
            String signValid = calculateHMAC(timestamp + paramStr, cloudApplication.getAppSecret());
            if (!sign.equals(signValid)) {
                log.warn("签名验证失败: 签名不匹配, URI: {}, AppId: {}, ExpectedSign: {}, ActualSign: {}",
                        path, appId, signValid, sign);
                sendError(httpServletResponse, HttpServletResponse.SC_UNAUTHORIZED, "认证失败，验签失败");
                return;
            }
            // 8. 签名去重（防重放）
            String signKey = "api_sign:" + sign;
            boolean firstUse = redisUtils.set(signKey, "1", 5 * 60); // 5分钟
            if (!firstUse) {
                log.warn("签名验证失败: 签名重复使用, URI: {}, AppId: {}, Sign: {}", path, appId, sign);
                sendError(httpServletResponse, HttpServletResponse.SC_UNAUTHORIZED, "签名已被使用，请勿重放请求");
                return;
            }

            if ("POST".equalsIgnoreCase(httpServletRequest.getMethod())) {
                JSONObject paramJson = JSONObject.parseObject(paramStr);
                paramJson.put("cloudApplicationId", cloudApplication.getId());
                ModifiableRequestWrapper wrappedRequest = new ModifiableRequestWrapper(httpServletRequest, paramJson.toJSONString());
                filterChain.doFilter(wrappedRequest, httpServletResponse);
            }else {
                filterChain.doFilter(httpServletRequest, httpServletResponse);
            }
        } catch (Exception e) {
            log.error("验签服务异常:", e);
            sendError(httpServletResponse, HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "服务异常");
        }
    }

    /**
     * 统一异常返回
     */
    private void sendError(HttpServletResponse response, int status, String message) throws IOException {
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(status);
        JSONObject result = new JSONObject();
        result.put("code", status);
        result.put("msg", message);
        response.getWriter().write(result.toJSONString());
        response.getWriter().flush();
    }

    /**
     * 获取客户端真实IP，支持多级代理
     */
    private String getClientIp(HttpServletRequest request) {
        String ip = request.getHeader("X-Forwarded-For");
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        if (ip != null && ip.contains(",")) {
            ip = ip.split(",")[0].trim();
        }
        return ip;
    }
}
