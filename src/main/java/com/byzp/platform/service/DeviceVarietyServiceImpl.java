package com.byzp.platform.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.DeviceVarietyDTO;
import com.byzp.platform.mapper.DeviceVarietyMapper;
import com.byzp.platform.mapper.po.DeviceVariety;
import com.byzp.platform.param.DeviceVarietyParam;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class DeviceVarietyServiceImpl {
    @Resource
    private DeviceVarietyMapper deviceVarietyMapper;


    public Page<DeviceVarietyDTO> queryPage(DeviceVarietyParam param){
        Page<DeviceVariety> page = new Page<>(param.getPageNo(), param.getPageSize());
        return deviceVarietyMapper.queryPage(page, param);
    }

    public DeviceVarietyDTO getByModel(String modelName){
        DeviceVarietyDTO res = new DeviceVarietyDTO();
        DeviceVariety deviceVariety = deviceVarietyMapper.selectOne(new QueryWrapper<DeviceVariety>()
                .lambda().eq(DeviceVariety::getModel, modelName)
                .eq(DeviceVariety::getIsDelete, 0));
        if (deviceVariety != null){
            BeanUtils.copyProperties(deviceVariety, res);
            return res;
        }
        return null;
    }
}
