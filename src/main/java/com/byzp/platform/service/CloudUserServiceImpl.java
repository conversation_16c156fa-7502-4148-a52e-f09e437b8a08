package com.byzp.platform.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSONObject;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byzp.platform.dto.CloudUserDTO;
import com.byzp.kylin.core.abnormal.KylinBusinessException;
import com.byzp.platform.mapper.CloudUserMapper;
import com.byzp.platform.mapper.ShortMessageSendRecordMapper;
import com.byzp.platform.mapper.po.CloudUser;
import com.byzp.platform.mapper.po.ShortMessageSendRecord;
import com.byzp.platform.param.CloudUserParam;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.UUID;

@Service
public class CloudUserServiceImpl {
    @Resource
    private CloudUserMapper cloudUserMapper;
    @Resource
    private ShortMessageSendRecordMapper shortMessageSendRecordMapper;

    public CloudUserDTO registerAndLogin(CloudUserParam param){
        CloudUserDTO result = new CloudUserDTO();
        Integer count = cloudUserMapper.selectCount(new QueryWrapper<CloudUser>()
                .lambda().eq(CloudUser::getAccount, param.getAccount())
                .eq(CloudUser::getIsDelete, 0));
        if (count > 0){
            throw new KylinBusinessException(500, "账号" + param.getAccount() + "已注册，请直接登录");
        }
        ShortMessageSendRecord shortMessageSendRecord = shortMessageSendRecordMapper.selectOne(new QueryWrapper<ShortMessageSendRecord>()
                .lambda().eq(ShortMessageSendRecord::getSmsType, 1)
                .eq(ShortMessageSendRecord::getMobile, param.getAccount())
                .orderByDesc(ShortMessageSendRecord::getId).last("limit 1"));
        if (shortMessageSendRecord == null){
            throw new KylinBusinessException(500, "未获取到验证码发送记录");
        }
        JSONObject sendParam = JSONObject.parseObject(shortMessageSendRecord.getSendParam());
        String validCode = sendParam.getString("code");
        if (!param.getValidCode().equals(validCode)){
            throw new KylinBusinessException(500, "验证码错误");
        }
        CloudUser cloudUser = new CloudUser();
        cloudUser.setAccount(param.getAccount());
        cloudUser.setPassword(param.getPassword());
        cloudUser.setIsDelete(0);
        cloudUser.setCreateTime(new Date());
        cloudUser.setUpdateTime(new Date());
        cloudUserMapper.insert(cloudUser);
        result.setId(cloudUser.getId());
        result.setAccount(param.getAccount());
        result.setJwtToken(generateJwtToken(cloudUser));
        return result;
    }

    public CloudUserDTO loginByPassword(CloudUserParam param){
        CloudUserDTO result = new CloudUserDTO();
        CloudUser cloudUser = cloudUserMapper.selectOne(new QueryWrapper<CloudUser>()
                .lambda().eq(CloudUser::getAccount, param.getAccount())
                .eq(CloudUser::getPassword, param.getPassword())
                .eq(CloudUser::getIsDelete, 0));
        if (cloudUser == null){
            throw new KylinBusinessException(500, "账号或密码错误");
        }
        result.setId(cloudUser.getId());
        result.setAccount(cloudUser.getAccount());
        result.setJwtToken(generateJwtToken(cloudUser));
        return result;
    }

    public CloudUserDTO loginByValidCode(CloudUserParam param){
        ShortMessageSendRecord shortMessageSendRecord = shortMessageSendRecordMapper.selectOne(new QueryWrapper<ShortMessageSendRecord>()
                .lambda().eq(ShortMessageSendRecord::getSmsType, 1)
                .eq(ShortMessageSendRecord::getMobile, param.getAccount())
                .orderByDesc(ShortMessageSendRecord::getId).last("limit 1"));
        if (shortMessageSendRecord == null){
            throw new KylinBusinessException(500, "未获取到验证码发送记录");
        }
        JSONObject sendParam = JSONObject.parseObject(shortMessageSendRecord.getSendParam());
        String validCode = sendParam.getString("code");
        if (!param.getValidCode().equals(validCode)){
            throw new KylinBusinessException(500, "验证码错误");
        }
        if (System.currentTimeMillis()/1000 - shortMessageSendRecord.getCreateTime().getTime()/1000 > 300){
            throw new KylinBusinessException(500, "验证码已过期");
        }
        CloudUserDTO result = new CloudUserDTO();
        CloudUser cloudUser = cloudUserMapper.selectOne(new QueryWrapper<CloudUser>()
                .lambda().eq(CloudUser::getAccount, param.getAccount())
                .eq(CloudUser::getIsDelete, 0));
        if (cloudUser == null){
            throw new KylinBusinessException(500, "账号信息不存在");
        }
        result.setId(cloudUser.getId());
        result.setAccount(param.getAccount());
        result.setJwtToken(generateJwtToken(cloudUser));
        return result;
    }


    public String generateJwtToken(CloudUser cloudUser){
        String token = JWT.create().withJWTId(UUID.randomUUID().toString()) // 设置唯一标识
                .withIssuer(Convert.toStr(cloudUser.getId())) // 设置签发者
                .withSubject(cloudUser.getAccount()) // 设置用户名
                .withClaim("mobile", cloudUser.getAccount())
                .withExpiresAt(new Date(System.currentTimeMillis() + 2592000l * 1000)) // 设置过期时间
                .withClaim("key", "base_jwt").sign(Algorithm.HMAC256("xiaopin-base-secret-4500aff6d7104b6588bc60783d45777e"));
        return token;
    }
}
