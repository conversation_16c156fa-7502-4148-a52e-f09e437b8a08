package com.byzp.platform.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.kylin.util.collection.CollectionUtils;
import com.byzp.platform.dto.CloudLocationDTO;
import com.byzp.kylin.core.abnormal.KylinBusinessException;
import com.byzp.platform.dto.CloudLocationGroupDTO;
import com.byzp.platform.mapper.CloudDeviceMapper;
import com.byzp.platform.mapper.CloudLocationGroupMapper;
import com.byzp.platform.mapper.CloudLocationMapper;
import com.byzp.platform.mapper.po.CloudDevice;
import com.byzp.platform.mapper.po.CloudLocation;
import com.byzp.platform.mapper.po.CloudLocationGroup;
import com.byzp.platform.param.CloudLocationParam;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class CloudLocationServiceImpl {

    @Resource
    private CloudLocationMapper cloudLocationMapper;
    @Resource
    private CloudLocationGroupMapper cloudLocationGroupMapper;
    @Resource
    private CloudDeviceMapper cloudDeviceMapper;

    public Long addOrUpdateForUser(CloudLocationParam param){
        if (param.getId() == null){
            CloudLocation cloudLocation = new CloudLocation();
            BeanUtils.copyProperties(param, cloudLocation);
            cloudLocation.setCreateTime(new Date());
            cloudLocationMapper.insert(cloudLocation);
            Long locationId = cloudLocation.getId();
            CloudLocationGroup cloudLocationGroup = new CloudLocationGroup();
            cloudLocationGroup.setLocationId(locationId);
            cloudLocationGroup.setName("默认分组");
            cloudLocationGroup.setCreateTime(new Date());
            cloudLocationGroup.setCloudApplicationId(param.getCloudApplicationId());
            cloudLocationGroupMapper.insert(cloudLocationGroup);
            return locationId;
        }else {
            CloudLocation oldCloudLocation = cloudLocationMapper.selectOne(new QueryWrapper<CloudLocation>()
                    .lambda().eq(CloudLocation::getId, param.getId())
                    .eq(CloudLocation::getCloudApplicationId, param.getCloudApplicationId()));
            if (oldCloudLocation == null){
                throw new KylinBusinessException(500, "位置信息不存在");
            }
            if (!param.getName().equals(oldCloudLocation.getName())){
                Integer count = cloudLocationMapper.selectCount(new QueryWrapper<CloudLocation>()
                        .lambda().eq(CloudLocation::getCloudApplicationId, param.getCloudApplicationId())
                        .eq(CloudLocation::getUserId, param.getUserId())
                        .eq(CloudLocation::getName, param.getName()));
                if (count > 0){
                    throw new KylinBusinessException(500, "存在同名的位置信息");
                }
                oldCloudLocation.setName(param.getName());
            }
            oldCloudLocation.setRemark(param.getRemark());
            cloudLocationMapper.updateById(oldCloudLocation);
            return param.getId();
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void delete(CloudLocationParam param){
        Integer count = cloudDeviceMapper.selectCount(new QueryWrapper<CloudDevice>().lambda().eq(CloudDevice::getCloudLocationId, param.getId()));
        if (count > 0){
            throw new KylinBusinessException(500, "存在关联的设备信息，无法删除");
        }
        cloudLocationMapper.delete(new QueryWrapper<CloudLocation>()
                .lambda().eq(CloudLocation::getId, param.getId())
                .eq(CloudLocation::getCloudApplicationId, param.getCloudApplicationId()));
        cloudLocationGroupMapper.delete(new QueryWrapper<CloudLocationGroup>().lambda().eq(CloudLocationGroup::getLocationId, param.getId()));
    }

    public List<CloudLocationDTO> queryList(CloudLocationParam param){
        List<CloudLocationDTO> recordList = cloudLocationMapper.queryList(param);
        if (CollectionUtils.isNotEmpty(recordList)) {
            for (CloudLocationDTO record : recordList) {
                List<CloudLocationGroupDTO> groupList = cloudLocationGroupMapper.selectListByLocationId(record.getId());
                record.setGroupList(groupList);
            }
        }
        return recordList;
    }

    public Page<CloudLocationDTO> queryPage(CloudLocationParam param){
        Page<CloudLocation> page = new Page<>(param.getPageNo(), param.getPageSize());
        Page<CloudLocationDTO> pageResult = cloudLocationMapper.queryPage(page, param);
        if (CollectionUtils.isNotEmpty(pageResult.getRecords())){
            for (CloudLocationDTO record : pageResult.getRecords()) {
                List<CloudLocationGroupDTO> groupList = cloudLocationGroupMapper.selectListByLocationId(record.getId());
                record.setGroupList(groupList);
                Integer groupNum = CollectionUtils.isNotEmpty(record.getGroupList())?record.getGroupList().size():0;
                Integer deviceNum = cloudDeviceMapper.selectCount(new QueryWrapper<CloudDevice>()
                        .lambda().eq(CloudDevice::getCloudLocationId, record.getId()));
                record.setGroupNum(groupNum);
                record.setDeviceNum(deviceNum);
            }
        }
        return pageResult;
    }
}
