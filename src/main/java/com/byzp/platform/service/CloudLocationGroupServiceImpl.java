package com.byzp.platform.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.kylin.core.abnormal.KylinBusinessException;
import com.byzp.kylin.util.collection.CollectionUtils;
import com.byzp.platform.dto.CloudLocationGroupDTO;
import com.byzp.platform.mapper.CloudDeviceMapper;
import com.byzp.platform.mapper.CloudLocationGroupMapper;
import com.byzp.platform.mapper.po.CloudDevice;
import com.byzp.platform.mapper.po.CloudLocationGroup;
import com.byzp.platform.param.CloudLocationGroupParam;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Date;

@Service
public class CloudLocationGroupServiceImpl {
    @Resource
    private CloudLocationGroupMapper cloudLocationGroupMapper;
    @Resource
    private CloudDeviceMapper cloudDeviceMapper;

    public void addOrUpdateForUser(CloudLocationGroupParam param){
        if (param.getId() == null){
            Integer count = cloudLocationGroupMapper.selectCount(new QueryWrapper<CloudLocationGroup>()
                    .lambda().eq(CloudLocationGroup::getCloudApplicationId, param.getCloudApplicationId())
                    .eq(CloudLocationGroup::getLocationId, param.getLocationId())
                    .eq(CloudLocationGroup::getName, param.getName()));
            if (count > 0){
                throw new KylinBusinessException(500, "存在同名的分组信息");
            }
            CloudLocationGroup cloudLocationGroup = new CloudLocationGroup();
            BeanUtils.copyProperties(param, cloudLocationGroup);
            cloudLocationGroup.setCreateTime(new Date());
            cloudLocationGroupMapper.insert(cloudLocationGroup);
        }else {
            CloudLocationGroup oldOne = cloudLocationGroupMapper.selectOne(new QueryWrapper<CloudLocationGroup>()
                    .lambda().eq(CloudLocationGroup::getCloudApplicationId, param.getCloudApplicationId())
                    .eq(CloudLocationGroup::getLocationId, param.getLocationId())
                    .eq(CloudLocationGroup::getId, param.getId()));
            if (oldOne == null){
                throw new KylinBusinessException(500, "分组信息不存在");
            }
            if (!param.getName().equals(oldOne.getName())){
                Integer count = cloudLocationGroupMapper.selectCount(new QueryWrapper<CloudLocationGroup>()
                        .lambda().eq(CloudLocationGroup::getCloudApplicationId, param.getCloudApplicationId())
                        .eq(CloudLocationGroup::getLocationId, param.getLocationId())
                        .eq(CloudLocationGroup::getName, param.getName()));
                if (count > 0){
                    throw new KylinBusinessException(500, "存在同名的分组信息");
                }
                oldOne.setName(param.getName());
            }
            oldOne.setRemark(param.getRemark());
            cloudLocationGroupMapper.updateById(oldOne);
        }
    }

    public void delete(CloudLocationGroupParam param){
        cloudLocationGroupMapper.delete(new QueryWrapper<CloudLocationGroup>()
                .lambda().eq(CloudLocationGroup::getId, param.getId())
                .eq(CloudLocationGroup::getCloudApplicationId, param.getCloudApplicationId()));
    }

    public Page<CloudLocationGroupDTO> queryPage(CloudLocationGroupParam param){
        Page<CloudLocationGroup> page = new Page<>(param.getPageNo(), param.getPageSize());
        Page<CloudLocationGroupDTO> cloudLocationGroupDTOPage = cloudLocationGroupMapper.queryPage(page, param);
        if (CollectionUtils.isNotEmpty(cloudLocationGroupDTOPage.getRecords())){
            for (CloudLocationGroupDTO record : cloudLocationGroupDTOPage.getRecords()) {
                Integer count = cloudDeviceMapper.selectCount(new QueryWrapper<CloudDevice>()
                        .lambda().eq(CloudDevice::getCloudLocationGroupId, record.getId()));
                record.setDeviceNum(count);
            }
        }
        return cloudLocationGroupDTOPage;
    }
}
