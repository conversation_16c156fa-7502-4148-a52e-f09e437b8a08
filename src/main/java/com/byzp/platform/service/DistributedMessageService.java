package com.byzp.platform.service;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.dto.DeviceConfigDTO;
import com.byzp.platform.utils.DeviceConfigWaitManager;
import com.byzp.platform.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 分布式消息服务
 * 用于处理跨服务器实例的消息通信
 */
@Service
@Slf4j
public class DistributedMessageService {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    
    @Resource
    private RedisUtils redisUtils;
    
    @Resource
    private DeviceConfigWaitManager deviceConfigWaitManager;
    
    /**
     * Redis发布订阅的频道前缀
     */
    private static final String DEVICE_CONFIG_CHANNEL = "device_config_response:";
    
    /**
     * 发布设备配置响应消息到Redis频道
     * 
     * @param deviceSerial 设备序列号
     * @param configData 设备配置数据
     */
    public void publishDeviceConfigResponse(String deviceSerial, DeviceConfigDTO configData) {
        try {
            String channel = redisUtils.getRedisAppKey(DEVICE_CONFIG_CHANNEL + deviceSerial);
            JSONObject message = new JSONObject();
            message.put("deviceSerial", deviceSerial);
            message.put("configData", configData);
            message.put("timestamp", System.currentTimeMillis());
            
            redisTemplate.convertAndSend(channel, message.toJSONString());
            log.info("发布设备配置响应消息到Redis频道，deviceSerial: {}, channel: {}", deviceSerial, channel);
        } catch (Exception e) {
            log.error("发布设备配置响应消息失败，deviceSerial: {}", deviceSerial, e);
        }
    }
    
    /**
     * 处理接收到的设备配置响应消息
     * 
     * @param channel 频道名称
     * @param message 消息内容
     */
    public void handleDeviceConfigResponse(String channel, String message) {
        try {
            JSONObject messageObj = JSONObject.parseObject(message);
            String deviceSerial = messageObj.getString("deviceSerial");
            JSONObject configDataObj = messageObj.getJSONObject("configData");
            
            if (deviceSerial != null && configDataObj != null) {
                DeviceConfigDTO configData = configDataObj.toJavaObject(DeviceConfigDTO.class);
                
                // 尝试完成本地的等待请求
                boolean completed = deviceConfigWaitManager.completeRequest(deviceSerial, configData);
                if (completed) {
                    log.info("通过Redis频道完成设备配置查询请求，deviceSerial: {}", deviceSerial);
                } else {
                    log.debug("本地无对应的等待请求，deviceSerial: {}", deviceSerial);
                }
            }
        } catch (Exception e) {
            log.error("处理设备配置响应消息失败，channel: {}, message: {}", channel, message, e);
        }
    }
    
    /**
     * 订阅设备配置响应频道
     * 
     * @param deviceSerial 设备序列号
     */
    public void subscribeDeviceConfigResponse(String deviceSerial) {
        try {
            String channel = redisUtils.getRedisAppKey(DEVICE_CONFIG_CHANNEL + deviceSerial);
            // 这里需要配置Redis消息监听器，在实际项目中通过配置类实现
            log.info("订阅设备配置响应频道，deviceSerial: {}, channel: {}", deviceSerial, channel);
        } catch (Exception e) {
            log.error("订阅设备配置响应频道失败，deviceSerial: {}", deviceSerial, e);
        }
    }
}
