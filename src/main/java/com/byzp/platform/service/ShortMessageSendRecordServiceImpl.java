package com.byzp.platform.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byzp.platform.dto.SmsSendResultDTO;
import com.byzp.kylin.core.abnormal.KylinBusinessException;
import com.byzp.platform.mapper.ShortMessageSendRecordMapper;
import com.byzp.platform.mapper.po.ShortMessageSendRecord;
import com.byzp.platform.utils.SmsSendUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Random;

@Service
public class ShortMessageSendRecordServiceImpl {
    @Resource
    private ShortMessageSendRecordMapper shortMessageSendRecordMapper;

    /**
     * 发送验证码短信
     * @param mobileNo
     */
    public void sendValidateCodeMessage(String mobileNo){
        String templateCode = "SMS_158790017";
        Random random = new Random();
        int x = random.nextInt(899999);
        String code = String.valueOf(x + 100000);
        JSONObject sendParam = new JSONObject();
        sendParam.put("code", code);
        SmsSendResultDTO smsSendResultDTO = SmsSendUtil.sendSms(mobileNo, sendParam.toJSONString(), templateCode);
        if (smsSendResultDTO.getSuccess()){
            ShortMessageSendRecord smsSendRecord = new ShortMessageSendRecord();
            smsSendRecord.setMobile(mobileNo);
            smsSendRecord.setSendParam(sendParam.toJSONString());
            smsSendRecord.setSmsType(1);
            smsSendRecord.setSendResponse(JSONObject.toJSONString(smsSendResultDTO));
            smsSendRecord.setCreateTime(new Date());
            smsSendRecord.setUpdateTime(new Date());
            shortMessageSendRecordMapper.insert(smsSendRecord);
        }else {
            throw new KylinBusinessException(500, "验证码发送失败");
        }
    }

    public void validateCode(String mobileNo, String validateCode){
        ShortMessageSendRecord shortMessageSendRecord = shortMessageSendRecordMapper.selectOne(new QueryWrapper<ShortMessageSendRecord>()
                .lambda().eq(ShortMessageSendRecord::getMobile, mobileNo)
                .eq(ShortMessageSendRecord::getSmsType, 1)
                .orderByDesc(ShortMessageSendRecord::getId)
                .last("limit 1"));
        if (shortMessageSendRecord == null){
            throw new KylinBusinessException(500, "未获取到系统验证码信息，请重新发送");
        }
        JSONObject codeInfo = JSONObject.parseObject(shortMessageSendRecord.getSendParam());
        if (!validateCode.equals(codeInfo.getString("code"))){
            throw new KylinBusinessException(500, "验证码错误");
        }
        if (System.currentTimeMillis()/1000 - shortMessageSendRecord.getCreateTime().getTime()/1000 > 300){
            throw new KylinBusinessException(500, "验证码已过期");
        }
    }
}
