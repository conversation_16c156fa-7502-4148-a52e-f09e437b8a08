package com.byzp.platform.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.kylin.core.abnormal.KylinBusinessException;
import com.byzp.kylin.util.collection.CollectionUtils;
import com.byzp.platform.dto.CloudVoiceInfoDTO;
import com.byzp.platform.mapper.CloudApplicationMapper;
import com.byzp.platform.mapper.CloudDeviceVoiceBindMapper;
import com.byzp.platform.mapper.CloudVoiceInfoMapper;
import com.byzp.platform.mapper.po.CloudApplication;
import com.byzp.platform.mapper.po.CloudDeviceVoiceBind;
import com.byzp.platform.mapper.po.CloudVoiceInfo;
import com.byzp.platform.param.CloudVoiceInfoParam;
import com.byzp.platform.utils.kdxf.KdxfUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Service
public class VoiceInfoServiceImpl {

    @Resource
    private CloudVoiceInfoMapper cloudVoiceInfoMapper;
    @Resource
    private CloudApplicationMapper cloudApplicationMapper;
    @Resource
    private CloudDeviceVoiceBindMapper cloudDeviceVoiceBindMapper;
    @Resource
    private CloudApplicationServiceImpl cloudApplicationService;

    public void saveVoice(CloudVoiceInfoParam param){
        CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
        if (cloudApplication == null){
            throw new KylinBusinessException(500, "应用信息不存在");
        }
        Integer count = cloudVoiceInfoMapper.selectCount(new QueryWrapper<CloudVoiceInfo>()
                .lambda().eq(CloudVoiceInfo::getVoiceId, param.getVoiceId())
                .eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId()));
        if (count > 0){
            CloudVoiceInfo cloudVoiceInfo = cloudVoiceInfoMapper.selectOne(new QueryWrapper<CloudVoiceInfo>()
                    .lambda().eq(CloudVoiceInfo::getVoiceId, param.getVoiceId())
                    .eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId()));
            cloudVoiceInfo.setVoiceBase64(param.getVoiceBase64());
            param.setVoiceBase64(null);
            List<CloudDeviceVoiceBind> bindList = cloudDeviceVoiceBindMapper.selectList(new QueryWrapper<CloudDeviceVoiceBind>()
                    .lambda().eq(CloudDeviceVoiceBind::getCloudApplicationId, param.getCloudApplicationId())
                    .eq(CloudDeviceVoiceBind::getVoiceId, param.getVoiceId()));
            if (CollectionUtils.isNotEmpty(bindList)){
                for (CloudDeviceVoiceBind bind : bindList) {
                    KdxfUtil.updateFeature(bind.getDeviceSerial(), param.getVoiceId(), param.getVoiceId(), cloudVoiceInfo.getVoiceBase64());
                }
            }
            cloudVoiceInfo.setVoiceName(param.getVoiceName());
            cloudVoiceInfo.setRemark(param.getRemark());
            cloudVoiceInfoMapper.update(cloudVoiceInfo, new QueryWrapper<CloudVoiceInfo>()
                    .lambda().eq(CloudVoiceInfo::getVoiceId, param.getVoiceId())
                    .eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId()));
        }else {
            CloudVoiceInfo cloudVoiceInfo = new CloudVoiceInfo();
            BeanUtils.copyProperties(param, cloudVoiceInfo);
            cloudVoiceInfo.setCreateTime(new Date());
            param.setVoiceBase64(null);
            cloudVoiceInfoMapper.insert(cloudVoiceInfo);
        }
    }

    public Page<CloudVoiceInfoDTO> queryPage(CloudVoiceInfoParam param){
        Page<CloudVoiceInfo> page = new Page<>(param.getPageNo(), param.getPageSize());
        return cloudVoiceInfoMapper.queryPage(page, param);
    }

    public void updateVoice(CloudVoiceInfoParam param){
        CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
        if (cloudApplication == null){
            throw new KylinBusinessException(500, "应用信息不存在");
        }
        CloudVoiceInfo cloudVoiceInfo = cloudVoiceInfoMapper.selectOne(new QueryWrapper<CloudVoiceInfo>()
                .lambda().eq(CloudVoiceInfo::getVoiceId, param.getVoiceId())
                .eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId()));
        if (cloudVoiceInfo == null){
            throw new KylinBusinessException(500, "声纹信息不存在");
        }
        cloudVoiceInfo.setVoiceBase64(param.getVoiceBase64());
        param.setVoiceBase64(null);
//        KdxfUtil.updateFeature(cloudApplication.getAppId(), param.getVoiceId(), param.getVoiceName(),cloudVoiceInfo.getVoiceBase64());
        List<CloudDeviceVoiceBind> bindList = cloudDeviceVoiceBindMapper.selectList(new QueryWrapper<CloudDeviceVoiceBind>()
                .lambda().eq(CloudDeviceVoiceBind::getCloudApplicationId, param.getCloudApplicationId())
                .eq(CloudDeviceVoiceBind::getVoiceId, param.getVoiceId()));
        if (CollectionUtils.isNotEmpty(bindList)){
            for (CloudDeviceVoiceBind bind : bindList) {
                KdxfUtil.updateFeature(bind.getDeviceSerial(), param.getVoiceId(), param.getVoiceId(), cloudVoiceInfo.getVoiceBase64());
            }
        }
        cloudVoiceInfo.setVoiceName(param.getVoiceName());
        cloudVoiceInfo.setRemark(param.getRemark());
        cloudVoiceInfoMapper.update(cloudVoiceInfo, new QueryWrapper<CloudVoiceInfo>()
                .lambda().eq(CloudVoiceInfo::getVoiceId, param.getVoiceId())
                .eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId()));
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteVoice(CloudVoiceInfoParam param){
        CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
        if (cloudApplication == null){
            throw new KylinBusinessException(500, "应用信息不存在");
        }
        Integer count = cloudDeviceVoiceBindMapper.selectCount(new QueryWrapper<CloudDeviceVoiceBind>()
                .lambda().eq(CloudDeviceVoiceBind::getCloudApplicationId, param.getCloudApplicationId())
                .eq(CloudDeviceVoiceBind::getVoiceId, param.getVoiceId()));
        if (count > 0){
            throw new KylinBusinessException(500, "该声纹已存在绑定的硬件，请先删除绑定关系后再操作");
        }
        cloudVoiceInfoMapper.delete(new QueryWrapper<CloudVoiceInfo>()
                .lambda().eq(CloudVoiceInfo::getVoiceId, param.getVoiceId())
                .eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId()));
        List<CloudDeviceVoiceBind> voiceBindList = cloudDeviceVoiceBindMapper.selectList(new QueryWrapper<CloudDeviceVoiceBind>()
                .lambda().eq(CloudDeviceVoiceBind::getVoiceId, param.getVoiceId())
                .eq(CloudDeviceVoiceBind::getCloudApplicationId, param.getCloudApplicationId()));
        if (CollectionUtils.isNotEmpty(voiceBindList)){
            for (CloudDeviceVoiceBind bindInfo : voiceBindList) {
                KdxfUtil.deleteFeature(bindInfo.getDeviceSerial(), bindInfo.getVoiceId());
                cloudDeviceVoiceBindMapper.deleteById(bindInfo.getId());
            }
        }
    }

    public CloudVoiceInfoDTO queryVoiceInfo(String voiceId, Long applicationId){
        if (applicationId == null){
            applicationId = cloudApplicationService.getApplicationIdByAppId();
        }
        CloudVoiceInfo cloudVoiceInfo = cloudVoiceInfoMapper.selectOne(new QueryWrapper<CloudVoiceInfo>()
                .lambda().eq(CloudVoiceInfo::getVoiceId, voiceId)
                .eq(CloudVoiceInfo::getCloudApplicationId, applicationId));
        if (cloudVoiceInfo == null){
            throw new KylinBusinessException(500, "声纹信息不存在");
        }
        CloudVoiceInfoDTO result = new CloudVoiceInfoDTO();
        BeanUtils.copyProperties(cloudVoiceInfo, result);
        return result;
    }
}
