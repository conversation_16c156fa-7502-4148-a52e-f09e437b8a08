package com.byzp.platform.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudApplicationDTO;
import com.byzp.platform.dto.CloudUserDTO;
import com.byzp.kylin.core.abnormal.KylinBusinessException;
import com.byzp.platform.mapper.CloudApplicationMapper;
import com.byzp.platform.mapper.CloudDeviceMapper;
import com.byzp.platform.mapper.CloudLocationMapper;
import com.byzp.platform.mapper.SleepReportMapper;
import com.byzp.platform.mapper.po.CloudApplication;
import com.byzp.platform.mapper.po.CloudDevice;
import com.byzp.platform.mapper.po.CloudLocation;
import com.byzp.platform.mapper.po.SleepReport;
import com.byzp.platform.param.CloudApplicationParam;
import com.byzp.platform.utils.AuthUtils;
import com.byzp.platform.utils.kdxf.KdxfUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Date;
import java.util.HexFormat;

@Service
public class CloudApplicationServiceImpl {
    @Resource
    private CloudApplicationMapper cloudApplicationMapper;
    @Resource
    private CloudLocationMapper cloudLocationMapper;
    @Resource
    private CloudDeviceMapper cloudDeviceMapper;
    @Resource
    private SleepReportMapper sleepReportMapper;

    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdateApplication(CloudUserDTO user, CloudApplicationParam param){
        if (param.getId() == null){
            Integer count = cloudApplicationMapper.selectCount(new QueryWrapper<CloudApplication>()
                    .lambda().eq(CloudApplication::getCloudUserId, user.getId())
                    .eq(CloudApplication::getAppName, param.getAppName()));
            if (count > 0){
                throw new KylinBusinessException(500, "账号下已存在同名应用");
            }
            CloudApplication cloudApplication = new CloudApplication();
            BeanUtils.copyProperties(param, cloudApplication);
            cloudApplication.setAppId(generateAppId(param.getAppName() + user.getId() + System.currentTimeMillis()));
            cloudApplication.setAppSecret(generateSecret());
            cloudApplication.setCreateTime(new Date());
            cloudApplication.setUpdateTime(new Date());
            cloudApplication.setCloudUserId(user.getId());
            cloudApplicationMapper.insert(cloudApplication);
//            KdxfUtil.createGroup(cloudApplication.getAppId(), cloudApplication.getAppName(), cloudApplication.getAppName());
        }else {
            CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getId());
            if (cloudApplication == null){
                throw new KylinBusinessException(500, "应用信息不存在");
            }
            if (!param.getAppName().equals(cloudApplication.getAppName())){
                Integer count = cloudApplicationMapper.selectCount(new QueryWrapper<CloudApplication>()
                        .lambda().eq(CloudApplication::getCloudUserId, user.getId())
                        .eq(CloudApplication::getAppName, param.getAppName()));
                if (count > 0){
                    throw new KylinBusinessException(500, "账号下已存在同名应用");
                }
                cloudApplication.setAppName(param.getAppName());
            }
            cloudApplication.setRemark(param.getRemark());
            cloudApplication.setNoticeCallbackUrl(param.getNoticeCallbackUrl());
            cloudApplication.setUpdateTime(new Date());
            cloudApplication.setVoiceSimilarity(param.getVoiceSimilarity());
            cloudApplicationMapper.updateById(cloudApplication);
        }
    }

    public void refreshSecret(CloudApplicationParam param){
        CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getId());
        if (cloudApplication == null){
            throw new KylinBusinessException(500, "应用信息不存在");
        }
        String newSecret = generateSecret();
        cloudApplication.setAppSecret(newSecret);
        cloudApplicationMapper.updateById(cloudApplication);
    }

    public String generateAppId(String key){
        try {
            // 使用 SHA-256 哈希算法
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hashBytes = digest.digest(key.getBytes());
            // 将哈希结果转为十六进制字符串（可截取部分作为 appId）
            String hexHash = HexFormat.of().formatHex(hashBytes);
            return hexHash.substring(0, 16); // 取前16位
        } catch (NoSuchAlgorithmException e) {
            throw new KylinBusinessException(500, "生成appId异常");
        }
    }

    public String generateSecret(){
        try {
            KeyGenerator keyGen = KeyGenerator.getInstance("HmacSHA256");
            keyGen.init(256);
            SecretKey secretKey = keyGen.generateKey();
            String appSecret = Base64.getEncoder().encodeToString(secretKey.getEncoded());
            return appSecret;
        }catch (Exception e){
            throw new KylinBusinessException(500, "生成appSecret异常");
        }
    }

    public void delete(CloudUserDTO user, CloudApplicationParam param){
        Integer count = cloudLocationMapper.selectCount(new QueryWrapper<CloudLocation>().lambda().eq(CloudLocation::getCloudApplicationId, param.getId()));
        if (count > 0){
            throw new KylinBusinessException(500, "当前应用下存在配置的地点信息，无法删除");
        }
        cloudApplicationMapper.deleteById(param.getId());
    }

    public CloudApplication queryDetail(Long applicationId){
        return cloudApplicationMapper.selectById(applicationId);
    }

    public Page<CloudApplicationDTO> queryPage(CloudApplicationParam param){
        Page<CloudApplication> page = new Page<>(param.getPageNo(), param.getPageSize());
        return cloudApplicationMapper.queryPage(page, param);
    }

    public CloudApplication getApplicationByDeviceSerial(String deviceSerial){
        CloudDevice cloudDevice = cloudDeviceMapper.selectOne(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getDeviceSerial, deviceSerial));
        if (cloudDevice != null){
            CloudApplication cloudApplication = cloudApplicationMapper.selectById(cloudDevice.getCloudApplicationId());
            return cloudApplication;
        }
        return null;
    }

    public void saveSleepReport(SleepReport report){
        sleepReportMapper.insert(report);
    }

    public Long getApplicationIdByAppId(){
        CloudApplication cloudApplication = cloudApplicationMapper.selectOne(new QueryWrapper<CloudApplication>().lambda().eq(CloudApplication::getAppId, AuthUtils.getAppId()));
        if (cloudApplication == null){
            throw new KylinBusinessException(500, "未获取到应用信息");
        }
        return cloudApplication.getId();
    }

}
