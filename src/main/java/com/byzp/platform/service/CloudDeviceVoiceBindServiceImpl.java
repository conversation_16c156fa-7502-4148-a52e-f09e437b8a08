package com.byzp.platform.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byzp.kylin.core.abnormal.KylinBusinessException;
import com.byzp.kylin.util.collection.CollectionUtils;
import com.byzp.platform.mapper.CloudApplicationMapper;
import com.byzp.platform.mapper.CloudDeviceMapper;
import com.byzp.platform.mapper.CloudDeviceVoiceBindMapper;
import com.byzp.platform.mapper.CloudVoiceInfoMapper;
import com.byzp.platform.mapper.po.CloudApplication;
import com.byzp.platform.mapper.po.CloudDevice;
import com.byzp.platform.mapper.po.CloudDeviceVoiceBind;
import com.byzp.platform.mapper.po.CloudVoiceInfo;
import com.byzp.platform.param.CloudDeviceVoiceBindParam;
import com.byzp.platform.utils.kdxf.KdxfUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CloudDeviceVoiceBindServiceImpl {
    @Resource
    private CloudDeviceVoiceBindMapper cloudDeviceVoiceBindMapper;
    @Resource
    private CloudVoiceInfoMapper cloudVoiceInfoMapper;
    @Resource
    private CloudApplicationMapper cloudApplicationMapper;
    @Resource
    private CloudDeviceMapper cloudDeviceMapper;

    @Transactional(rollbackFor = Exception.class)
    public void insertDeviceVoiceBind(CloudDeviceVoiceBindParam param){
        CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
        if (cloudApplication == null){
            throw new KylinBusinessException(500, "应用信息不存在");
        }

        Integer count = cloudDeviceMapper.selectCount(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getDeviceSerial, param.getDeviceSerial())
                .eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId()));
        if (count == 0){
            throw new KylinBusinessException(500, "应用下不存在该设备序列号");
        }
        if (CollectionUtils.isEmpty(param.getVoiceIdList())){
            throw new KylinBusinessException(500, "声纹id列表不能为空");
        }
        List<CloudVoiceInfo> cloudVoiceInfoList = new ArrayList<>();
        for (String voiceId : param.getVoiceIdList()) {
            List<CloudVoiceInfo> voiceInfoList = cloudVoiceInfoMapper.selectList(new QueryWrapper<CloudVoiceInfo>()
                    .lambda().eq(CloudVoiceInfo::getVoiceId, voiceId)
                    .eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId()));
            if (CollectionUtils.isEmpty(voiceInfoList)){
                throw new KylinBusinessException(500, "声纹信息不存在");
            }else if (voiceInfoList.size() > 1){
                throw new KylinBusinessException(500, "应用下存在重复的声纹id");
            }else {
                cloudVoiceInfoList.add(voiceInfoList.get(0));
            }
        }
        for (CloudVoiceInfo cloudVoiceInfo : cloudVoiceInfoList) {
            CloudDeviceVoiceBind insert = new CloudDeviceVoiceBind();
            BeanUtils.copyProperties(param, insert);
            insert.setCreateTime(new Date());
            KdxfUtil.createFeature(param.getDeviceSerial(), param.getVoiceId(),param.getVoiceId(), cloudVoiceInfo.getVoiceBase64());
            cloudDeviceVoiceBindMapper.insert(insert);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void insertDeviceVoiceBindV2(CloudDeviceVoiceBindParam param){
        CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
        if (cloudApplication == null){
            throw new KylinBusinessException(500, "应用信息不存在");
        }

        List<CloudDevice> cloudDeviceList = cloudDeviceMapper.selectList(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId()));
        if (CollectionUtils.isEmpty(cloudDeviceList)){
            throw new KylinBusinessException(500, "应用下设备列表为空");
        }
        Map<String, CloudDevice> cloudDeviceMap = new HashMap<>();
        cloudDeviceList.forEach(c->cloudDeviceMap.put(c.getDeviceSerial(), c));
        int voiceCount = cloudVoiceInfoMapper.selectCount(new QueryWrapper<CloudVoiceInfo>()
                .lambda().eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId()));
        if (voiceCount == 0){
            throw new KylinBusinessException(500, "应用下声纹列表为空");
        }
        Map<String, CloudVoiceInfo> cloudVoiceInfoMap = new HashMap<>();
        for (CloudDeviceVoiceBindParam bindInfo : param.getBindInfoList()) {
            List<CloudVoiceInfo> voiceInfoList = cloudVoiceInfoMapper.selectList(new QueryWrapper<CloudVoiceInfo>()
                    .lambda().eq(CloudVoiceInfo::getVoiceId, bindInfo.getVoiceId())
                    .eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId()));
            if (CollectionUtils.isEmpty(voiceInfoList)){
                throw new KylinBusinessException(500, "声纹信息不存在");
            }else if (voiceInfoList.size() > 1){
                throw new KylinBusinessException(500, "应用下存在重复的声纹id");
            }else {
                cloudVoiceInfoMap.put(bindInfo.getVoiceId(), voiceInfoList.get(0));
            }
            CloudDevice cloudDevice = cloudDeviceMap.get(bindInfo.getDeviceSerial());
            if (cloudDevice == null){
                throw new KylinBusinessException(500, "设备信息不存在,deviceSerial=" + bindInfo.getDeviceSerial());
            }
        }
        for (CloudDeviceVoiceBindParam bindInfo : param.getBindInfoList()) {
            CloudVoiceInfo cloudVoiceInfo = cloudVoiceInfoMap.get(bindInfo.getVoiceId());
            CloudDeviceVoiceBind insert = new CloudDeviceVoiceBind();
            BeanUtils.copyProperties(bindInfo, insert);
            insert.setCreateTime(new Date());
            insert.setCloudApplicationId(param.getCloudApplicationId());
            KdxfUtil.createFeature(bindInfo.getDeviceSerial(), bindInfo.getVoiceId(),bindInfo.getVoiceId(), cloudVoiceInfo.getVoiceBase64());
            cloudDeviceVoiceBindMapper.insert(insert);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteDeviceVoiceBind(CloudDeviceVoiceBindParam param){
        CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
        if (cloudApplication == null){
            throw new KylinBusinessException(500, "应用信息不存在");
        }
        Integer count = cloudDeviceMapper.selectCount(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getDeviceSerial, param.getDeviceSerial())
                .eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId()));
        if (count == 0){
            throw new KylinBusinessException(500, "应用下不存在该设备序列号");
        }
        List<CloudDeviceVoiceBind> bindList = cloudDeviceVoiceBindMapper.selectList(new QueryWrapper<CloudDeviceVoiceBind>()
                .lambda().eq(CloudDeviceVoiceBind::getDeviceSerial, param.getDeviceSerial())
                .in(CloudDeviceVoiceBind::getVoiceId, param.getVoiceIdList()));
        if (CollectionUtils.isNotEmpty(bindList)){
            for (CloudDeviceVoiceBind bindInfo : bindList) {
                KdxfUtil.deleteFeature(bindInfo.getDeviceSerial(), bindInfo.getVoiceId());
                cloudDeviceVoiceBindMapper.deleteById(bindInfo.getId());
            }
        }
    }

    public List<String> queryVoiceIdListByDeviceSerial(CloudDeviceVoiceBindParam param){
        List<String> result = new ArrayList<>();
        List<CloudDeviceVoiceBind> bindList = cloudDeviceVoiceBindMapper.selectList(new QueryWrapper<CloudDeviceVoiceBind>()
                .lambda().eq(CloudDeviceVoiceBind::getCloudApplicationId, param.getCloudApplicationId())
                .eq(CloudDeviceVoiceBind::getDeviceSerial, param.getDeviceSerial()));
        if (CollectionUtils.isNotEmpty(bindList)){
            result = bindList.stream().map(CloudDeviceVoiceBind::getVoiceId).collect(Collectors.toList());
        }
        return result;
    }

    public void bindOne(CloudDeviceVoiceBindParam param){
        CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
        if (cloudApplication == null){
            throw new KylinBusinessException(500, "应用信息不存在");
        }
        Integer deviceCount = cloudDeviceMapper.selectCount(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId())
                .eq(CloudDevice::getDeviceSerial, param.getDeviceSerial()));
        if (deviceCount == 0){
            throw new KylinBusinessException(500, "设备信息不存在");
        }
        CloudVoiceInfo cloudVoiceInfo = cloudVoiceInfoMapper.selectOne(new QueryWrapper<CloudVoiceInfo>()
                .lambda().eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId())
                .eq(CloudVoiceInfo::getVoiceId, param.getVoiceId()));
        if (cloudVoiceInfo == null){
            throw new KylinBusinessException(500, "声纹信息不存在");
        }
        CloudDeviceVoiceBind insert = new CloudDeviceVoiceBind();
        insert.setVoiceId(param.getVoiceId());
        insert.setDeviceSerial(param.getDeviceSerial());
        insert.setCreateTime(new Date());
        insert.setCloudApplicationId(param.getCloudApplicationId());
        KdxfUtil.createFeature(insert.getDeviceSerial(), insert.getVoiceId(),insert.getVoiceId(), cloudVoiceInfo.getVoiceBase64());
        cloudDeviceVoiceBindMapper.insert(insert);
    }

    public void unbindOne(CloudDeviceVoiceBindParam param){
        CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
        if (cloudApplication == null){
            throw new KylinBusinessException(500, "应用信息不存在");
        }
        Integer deviceCount = cloudDeviceMapper.selectCount(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId())
                .eq(CloudDevice::getDeviceSerial, param.getDeviceSerial()));
        if (deviceCount == 0){
            throw new KylinBusinessException(500, "设备信息不存在");
        }
        CloudVoiceInfo cloudVoiceInfo = cloudVoiceInfoMapper.selectOne(new QueryWrapper<CloudVoiceInfo>()
                .lambda().eq(CloudVoiceInfo::getCloudApplicationId, param.getCloudApplicationId())
                .eq(CloudVoiceInfo::getVoiceId, param.getVoiceId()));
        if (cloudVoiceInfo == null){
            throw new KylinBusinessException(500, "声纹信息不存在");
        }
        KdxfUtil.deleteFeature(param.getDeviceSerial(), param.getVoiceId());
        cloudDeviceVoiceBindMapper.delete(new QueryWrapper<CloudDeviceVoiceBind>()
                .lambda().eq(CloudDeviceVoiceBind::getCloudApplicationId, param.getCloudApplicationId())
                .eq(CloudDeviceVoiceBind::getVoiceId, param.getVoiceId()));
    }
}
