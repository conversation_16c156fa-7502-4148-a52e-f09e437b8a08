package com.byzp.platform.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.kylin.core.abnormal.KylinBusinessException;
import com.byzp.kylin.util.StringUtils;
import com.byzp.kylin.util.collection.CollectionUtils;
import com.byzp.platform.dto.CloudInkInfoDTO;
import com.byzp.platform.mapper.CloudDeviceMapper;
import com.byzp.platform.mapper.CloudInkInfoMapper;
import com.byzp.platform.mapper.po.CloudDevice;
import com.byzp.platform.mapper.po.CloudInkInfo;
import com.byzp.platform.param.CloudInkInfoParam;
import com.byzp.platform.utils.ModuloUtil;
import com.byzp.platform.utils.MqttUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.expression.operators.arithmetic.Modulo;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@Slf4j
public class CloudInkInfoServiceImpl {
    @Resource
    private CloudInkInfoMapper cloudInkInfoMapper;
    @Resource
    private CloudDeviceMapper cloudDeviceMapper;
    @Resource
    private MqttUtil mqttUtil;
    @Resource
    private CloudApplicationServiceImpl cloudApplicationService;

    public CloudInkInfo save(CloudInkInfoParam param) {
        if (param.getId() == null){
            Integer count = cloudInkInfoMapper.selectCount(new QueryWrapper<CloudInkInfo>()
                    .lambda().eq(CloudInkInfo::getInkName, param.getInkName())
                    .eq(CloudInkInfo::getCloudApplicationId, param.getCloudApplicationId()));
            if (count > 0){
                throw new KylinBusinessException(500, "墨水屏名称已存在");
            }
            CloudInkInfo insert = new CloudInkInfo();
            BeanUtils.copyProperties(param, insert);
            cloudInkInfoMapper.insert(insert);
            return insert;
        }else {
            CloudInkInfo cloudInkInfo = cloudInkInfoMapper.selectById(param.getId());
            if (cloudInkInfo == null){
                throw new KylinBusinessException(500, "墨水屏信息不存在");
            }
            if (!param.getInkName().equals(cloudInkInfo.getInkName())){
                Integer count = cloudInkInfoMapper.selectCount(new QueryWrapper<CloudInkInfo>()
                        .lambda().eq(CloudInkInfo::getInkName, param.getInkName())
                        .eq(CloudInkInfo::getCloudApplicationId, param.getCloudApplicationId()));
                if (count > 0){
                    throw new KylinBusinessException(500, "墨水屏名称已存在");
                }
            }
            String inkSettingInfo = getSetInkContent(param.getSettingInfo());
            CloudInkInfo update = new CloudInkInfo();
            BeanUtils.copyProperties(param, update);
            cloudInkInfoMapper.updateById(update);
            if (!param.getSettingInfo().equals(cloudInkInfo.getSettingInfo())){
                //给所有绑定的设备推送修改后的设置
                List<CloudDevice> cloudDeviceList = cloudDeviceMapper.selectList(new QueryWrapper<CloudDevice>()
                        .lambda().eq(CloudDevice::getCloudInkId, param.getId()));
                if (CollectionUtils.isNotEmpty(cloudDeviceList)){
                    for (CloudDevice cloudDevice : cloudDeviceList) {
                        mqttUtil.sendMsgToDevice(cloudDevice.getDeviceSerial(), inkSettingInfo);
                    }
                }
            }
            return update;
        }
    }

    public Page<CloudInkInfoDTO> queryPage(CloudInkInfoParam param){
        Page<CloudInkInfo> page = new Page<>(param.getPageNo(), param.getPageSize());
        if (param.getCloudApplicationId() == null) {
            param.setCloudApplicationId(cloudApplicationService.getApplicationIdByAppId());
        }
        Page<CloudInkInfoDTO> cloudInkInfoDTOPage = cloudInkInfoMapper.queryPage(page, param);
        if (CollectionUtils.isNotEmpty(cloudInkInfoDTOPage.getRecords())){
            Map<Long, Integer> inkIdCountMap = new HashMap<>();
            List<Long> cloudInkIdList = cloudInkInfoDTOPage.getRecords().stream().map(CloudInkInfoDTO::getId).toList();
            List<Map<Long, Integer>> mapList = cloudDeviceMapper.queryCountByInkIdList(cloudInkIdList);
            if (CollectionUtils.isNotEmpty(mapList)){
                inkIdCountMap = mapList.stream()
                        .collect(Collectors.toMap(
                                m -> ((Number)m.get("cloudInkId")).longValue(),
                                m -> ((Number)m.get("total")).intValue()
                        ));
            }
            for (CloudInkInfoDTO record : cloudInkInfoDTOPage.getRecords()) {
                Integer count = inkIdCountMap.get(record.getId());
                count = count == null?0:count;
                record.setRefDeviceNum(count);
            }
        }
        return cloudInkInfoDTOPage;
    }

    public void delete(CloudInkInfoParam param){
        Integer count = cloudDeviceMapper.selectCount(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getCloudInkId, param.getId()));
        if (count > 0){
            throw new KylinBusinessException(500, "当前墨水屏有关联设备，请取消关联后再操作");
        }
        cloudInkInfoMapper.selectById(param.getId());
    }

    public String querySource(Long id, Long cloudApplicationId){
        if (cloudApplicationId == null){
            cloudApplicationId = cloudApplicationService.getApplicationIdByAppId();
        }
        return cloudDeviceMapper.selectSourceDetail(id, cloudApplicationId);
    }

    public CloudInkInfoDTO refDeviceList(Long id, Long cloudApplicationId){
        CloudInkInfoDTO result = new CloudInkInfoDTO();
        List<CloudDevice> cloudDeviceList = cloudDeviceMapper.selectList(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getCloudInkId, id)
                .eq(CloudDevice::getCloudApplicationId, cloudApplicationId));
        result.setRefDeviceList(cloudDeviceList);
        return result;
    }

    public List<String> refDeviceSerialList(Long id, Long cloudApplicationId){
        List<String> result = new ArrayList<>();
        if (cloudApplicationId == null){
            cloudApplicationId = cloudApplicationService.getApplicationIdByAppId();
        }
        List<CloudDevice> cloudDeviceList = cloudDeviceMapper.selectList(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getCloudInkId, id)
                .eq(CloudDevice::getCloudApplicationId, cloudApplicationId));
        if (CollectionUtils.isNotEmpty(cloudDeviceList)){
            result = cloudDeviceList.stream().map(CloudDevice::getDeviceSerial).toList();
        }
        return result;
    }

    public void updateRefDeviceList(CloudInkInfoParam param){
        CloudInkInfo cloudInkInfo = cloudInkInfoMapper.selectById(param.getId());
        List<CloudDevice> reffedCloudDeviceList = cloudDeviceMapper.selectList(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getCloudInkId, param.getId())
                .eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId()));
        List<String> reffedDeviceSerialList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(reffedCloudDeviceList)){
            reffedDeviceSerialList = reffedCloudDeviceList.stream().map(CloudDevice::getDeviceSerial).toList();
            // 先删除所有关联关系
            cloudDeviceMapper.update(null, new UpdateWrapper<CloudDevice>()
                    .lambda().in(CloudDevice::getDeviceSerial, reffedDeviceSerialList)
                    .eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId())
                    .set(CloudDevice::getCloudInkId, null));
        }
        if (CollectionUtils.isNotEmpty(param.getDeviceSerialList())){
            // 设备序列号列表不为空，设置新的关联
            cloudDeviceMapper.update(null, new UpdateWrapper<CloudDevice>()
                    .lambda().in(CloudDevice::getDeviceSerial, param.getDeviceSerialList())
                            .eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId())
                    .set(CloudDevice::getCloudInkId, param.getId()));
            String settingInfo = getSetInkContent(cloudInkInfo.getSettingInfo());
            //给设备下发配置信息
            for (String deviceSerial : param.getDeviceSerialList()) {
                // 过滤掉之前就存在的关联关系
                if (!reffedDeviceSerialList.contains(deviceSerial)){
                    mqttUtil.sendMsgToDevice(deviceSerial, settingInfo);
                }
            }
        }
    }

    /**
     * 获取下发的配置内容
     * @param settingInfo 表中存储的配置信息
     * @return
     */
    public String getSetInkContent(String settingInfo){
        JSONObject result = new JSONObject();
        result.put("msgType", "setInkContent");
        JSONObject payload = new JSONObject();
        JSONObject tableContent = JSONObject.parseObject(settingInfo);
        payload.put("backgroundColor", tableContent.get("backgroundColor"));
        payload.put("width", tableContent.get("width"));
        payload.put("height", tableContent.get("height"));
        JSONArray elementArr = new JSONArray();
        JSONArray fieldArr = new JSONArray();
        JSONArray elements = tableContent.getJSONArray("elements");
        for (int i = 0; i < elements.size(); i++) {
            JSONObject element = elements.getJSONObject(i);
            String valueStr = element.getString("value");
            String fieldStr = element.getString("field");
            String type = element.getString("type");
            if (StringUtils.isNotBlank(valueStr)){
                // 加到elementArr 中
//                element.remove("type");
                element.remove("value");
                element.put("data", getModuloInfo(type, valueStr, element.getInteger("size")));
                elementArr.add(element);
                continue;
            }
            if (StringUtils.isNotBlank(fieldStr)){
                // 加到fieldArr中
//                element.remove("type");
                element.remove("field");
                element.put("name", fieldStr);
                fieldArr.add(element);
            }
        }
        payload.put("elements", elementArr);
        payload.put("fields", fieldArr);
        result.put("payload", payload);
        return result.toJSONString();
    }

    /**
     * 对模版里数据进行取模
     * @param type 类型：label-文字标签  qrcode-二维码
     * @param value 值（type 是 label时：明文，type是qrcode时：二维码的base64）
     * @param size
     * @return
     */
    public String getModuloInfo(String type, String value, Integer size){
        if ("label".equals(type)){
            return ModuloUtil.readFont(value, size);
        }else if ("qrcode".equals(type)){
            return value;
//            return ModuloUtil.readQrcode(value);
//            return ModuloUtil.readFont(value,16);
//            return ModuloUtil.readFont(ModuloUtil.readQrcode(value),16);
        }
        return "";
    }

    public void sendTest(String deviceSerial, Long id){
        CloudInkInfo cloudInkInfo = cloudInkInfoMapper.selectById(id);
        String setInkContent = getSetInkContent(cloudInkInfo.getSettingInfo());
        mqttUtil.sendMsgToDevice(deviceSerial, setInkContent);

    }

    public void sendContentToDevice(CloudInkInfoParam param){
        if (CollectionUtils.isNotEmpty(param.getDeviceSerialList())){
            CloudInkInfo cloudInkInfo = cloudInkInfoMapper.selectOne(new QueryWrapper<CloudInkInfo>()
                    .lambda().eq(CloudInkInfo::getId, param.getId())
                    .eq(CloudInkInfo::getCloudApplicationId, param.getCloudApplicationId()));
            if (cloudInkInfo == null){
                throw new KylinBusinessException(500, "未查询到墨水屏信息");
            }
            String setInkValue = getSetInkValue(param.getData(), cloudInkInfo.getSettingInfo());
            for (String deviceSerial : param.getDeviceSerialList()) {
                mqttUtil.sendMsgToDevice(deviceSerial, setInkValue);
            }
        }else {
            throw new KylinBusinessException(500, "设备序列号列表不能为空");
        }
    }

    /**
     *
     * @param data 准备发送的数据
     * @param settingInfo 表中存储的配置信息
     * @return
     */
    public String getSetInkValue(String data, String settingInfo){
        // 根据表中存储的配置信息，获取每个属性的类型
        JSONArray settingInfoArr = JSONObject.parseObject(settingInfo).getJSONArray("elements");
        Map<String, JSONObject> fieldTypeMap = new HashMap<>();
        for (int i = 0; i < settingInfoArr.size(); i++) {
            String fieldStr = settingInfoArr.getJSONObject(i).getString("field");

            if(StringUtils.isNotBlank(fieldStr)){
                fieldTypeMap.put(fieldStr, settingInfoArr.getJSONObject(i));
            }
        }
        JSONObject result = new JSONObject();
        result.put("msgType", "setInkValue");

        JSONArray payload = new JSONArray();
        JSONArray dataArr = JSONObject.parseArray(data);
        for (int i = 0; i < dataArr.size(); i++) {
            // 准备下发的json对象
            JSONObject toBeSend = dataArr.getJSONObject(i);
            // 待下发的json对象中的field的值
            String fieldName = toBeSend.getString("field");
            // 待下发的json对象中的value的值
            String value = toBeSend.getString("value");
            // 查询墨水屏配置中是否存在fieldName
            JSONObject fieldObj = fieldTypeMap.get(fieldName);
            if (fieldObj != null){
                String type = fieldObj.getString("type");
                Integer size = fieldObj.getInteger("size");
                String moduloStr = ModuloUtil.moduloStrByType(value, type, size);
                fieldObj.put("value", moduloStr);
                payload.add(fieldObj);
            }
        }
        result.put("payload", payload);
        log.info("msg:" + result);
        return result.toJSONString();
    }

}
