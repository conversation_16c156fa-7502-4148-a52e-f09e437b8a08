package com.byzp.platform.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byzp.platform.mapper.SleepReportMapper;
import com.byzp.platform.mapper.po.SleepReport;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
@Slf4j
public class CloudSleepReportServiceImpl {
    @Resource
    private SleepReportMapper sleepReportMapper;


    public SleepReport queryReport(String deviceSerial, String reportDate){
        return sleepReportMapper.selectOne(new QueryWrapper<SleepReport>()
                .lambda().eq(SleepReport::getDeviceSerial, deviceSerial)
                .eq(SleepReport::getReportDate, reportDate).orderByDesc(SleepReport::getId)
                .last("limit 1"));
    }
}
