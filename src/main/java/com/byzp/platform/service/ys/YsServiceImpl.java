package com.byzp.platform.service.ys;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.ys.YsClient;
import com.byzp.platform.utils.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Service
@Slf4j
public class YsServiceImpl {
    @Resource
    private YsClient ysClient;


    @Resource
    private RedisUtils redisUtils;



    public String getTokenFromCache(){
        Object ys_accessToken = redisUtils.get("ys_cloud_accessToken");
        if (ys_accessToken == null){
            return getTokenInTime();
        }else {
            return (String) ys_accessToken;
        }
    }

    public String getTokenInTime(){
        String res = null;
        JSONObject tokenRes = ysClient.getToken();
        if ("200".equals(tokenRes.getString("code"))){
            res = tokenRes.getJSONObject("data").getString("accessToken");
            Long expireTime = tokenRes.getJSONObject("data").getLong("expireTime");
            Date expireDate = new Date(expireTime - 7200*1000);
            redisUtils.set("ys_cloud_accessToken", res, expireDate);
        }else {
            // 重试
        }
        return res;
    }

    public JSONObject autoPermission(String deviceSerial){
        return ysClient.autoPermission(getTokenFromCache(), deviceSerial);
    }

    public JSONObject offlineConfirm(String deviceSerial){
        return ysClient.offlineConfirm(getTokenFromCache(), deviceSerial);
    }

    public JSONObject doPermission(String deviceSerial){
        return ysClient.doPermission(getTokenFromCache(), deviceSerial);
    }



    public JSONObject baseDeviceInfo(String deviceSerial){
        return ysClient.baseDeviceInfo(getTokenFromCache(), deviceSerial);
    }

}
