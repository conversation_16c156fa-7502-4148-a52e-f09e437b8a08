package com.byzp.platform.service;

import cn.hutool.core.convert.Convert;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.kylin.util.StringUtils;
import com.byzp.platform.client.ys.DeviceGatewayClient;
import com.byzp.platform.client.ys.HelpButtonClient;
import com.byzp.platform.client.ys.SleepSDNL1Client;
import com.byzp.platform.client.ys.YsClient;
import com.byzp.platform.dto.CloudDeviceDTO;
import com.byzp.kylin.core.abnormal.KylinBusinessException;
import com.byzp.kylin.util.collection.CollectionUtils;
import com.byzp.platform.dto.DeviceConfigDTO;
import com.byzp.platform.dto.ys.ChildDeviceParam;
import com.byzp.platform.utils.DeviceConfigWaitManager;

import java.util.concurrent.CompletableFuture;
import com.byzp.platform.dto.ys.YsResult;
import com.byzp.platform.mapper.*;
import com.byzp.platform.mapper.po.CloudApplication;
import com.byzp.platform.mapper.po.CloudDevice;
import com.byzp.platform.mapper.po.CloudLocationGroup;
import com.byzp.platform.mapper.po.DeviceVariety;
import com.byzp.platform.param.*;
import com.byzp.platform.service.ys.YsServiceImpl;
import com.byzp.platform.utils.MqttUtil;
import com.byzp.platform.utils.kdxf.KdxfUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalTime;
import java.util.*;

@Service
@Slf4j
public class CloudDeviceServiceImpl {
    @Resource
    private CloudDeviceMapper cloudDeviceMapper;
    @Resource
    private DeviceVarietyMapper deviceVarietyMapper;
    @Resource
    private YsClient ysClient;
    @Resource
    private YsServiceImpl ysService;
    @Resource
    private HelpButtonClient helpButtonClient;
    @Resource
    private SleepSDNL1Client sleepSDNL1Client;
    @Resource
    private DeviceGatewayClient deviceGatewayClient;
    @Resource
    private MqttUtil mqttUtil;

    @Resource
    private DeviceConfigWaitManager deviceConfigWaitManager;
    @Resource
    private CloudApplicationMapper cloudApplicationMapper;
    @Resource
    private CloudLocationMapper cloudLocationMapper;
    @Resource
    private CloudLocationGroupMapper cloudLocationGroupMapper;

    @Transactional(rollbackFor = Exception.class)
    public void addDevice(CloudDeviceParam param){
        DeviceVariety deviceVariety = deviceVarietyMapper.selectOne(new QueryWrapper<DeviceVariety>()
                .lambda().eq(DeviceVariety::getModel, param.getModel()));
        if (deviceVariety == null){
            throw new KylinBusinessException(500, "设备型号不存在");
        }
        if ("本源康养".equals(deviceVariety.getBrand())) {
            addOrUpdateCloudDevice(param);
            // 如果是睡眠雷达
            if ("BYZP-SMLD".equals(param.getModel())){
                // 订阅批量推送信息的topic
                subscribeBatchTopic(param);
                // 设置睡眠检测区间
//                setByzpSmldConfig(param);
            }
            // 如果是智能尿不湿
            if ("BYZP-ZNNBS".equals(param.getModel())){
                Integer count = cloudDeviceMapper.selectCount(new QueryWrapper<CloudDevice>()
                        .lambda().eq(CloudDevice::getDeviceSerial, param.getRelDeviceSerial())
                        .eq(CloudDevice::getModel, "BYZP-SMLD"));
                if (count <= 0){
                    throw new KylinBusinessException(500, "未获取到关联的设备信息");
                }
                configDiaper(param);
            }
        }else if ("萤石".equals(deviceVariety.getBrand())){
            // 先查询设备是否已绑定到公司萤石云账号下
            JSONObject cloudBindDeviceInfoRes = ysClient.singleDeviceInfo(ysService.getTokenFromCache(), param.getDeviceSerial());
            log.info("{}",cloudBindDeviceInfoRes);
            if ("200".equals(cloudBindDeviceInfoRes.getString("code")) || "10026".equals(cloudBindDeviceInfoRes.getString("code"))){
                // 设备已绑定到公司萤石云账号下，只需要在本源云平台保存该设备信息
                addOrUpdateCloudDevice(param);
            }else if ("20018".equals(cloudBindDeviceInfoRes.getString("code"))){
                if (StringUtils.isNotBlank(param.getValidateCode())){
                    // 将设备绑定到公司萤石云账号下
                    YsResult ysResult = ysClient.addDevice(ysService.getTokenFromCache(), param.getDeviceSerial(), param.getValidateCode());
                    if ("200".equals(ysResult.getCode())) {
                        //成功将萤石设备绑定到本源萤石账号下
                        addOrUpdateCloudDevice(param);

                    }else {
                        throw new KylinBusinessException(Integer.parseInt(ysResult.getCode()), ysResult.getMsg());
                    }
                }else {
                    throw new KylinBusinessException(20018, "萤石设备验证码不能为空");
                }
            }else {
                throw new KylinBusinessException(cloudBindDeviceInfoRes.getInteger("code"), cloudBindDeviceInfoRes.getString("msg"));
            }
            if ("CS-T3C-BG".equals(param.getModel())){
                enableButtonEmergencySwitch(param.getDeviceSerial());
            }

            if("CS-EP-SDNL1".equals(param.getModel())){
                setCsepSdnl1Config(param);
            }
        }
        if (deviceVariety.getNeedAudio() == 1){
            KdxfUtil.createGroup(param.getDeviceSerial(), param.getDeviceSerial(), param.getDeviceSerial());
        }
    }

    public void addOrUpdateCloudDevice(CloudDeviceParam param){
        CloudDevice device = cloudDeviceMapper.selectOne(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getDeviceSerial, param.getDeviceSerial()));
        CloudLocationGroup cloudLocationGroup = cloudLocationGroupMapper.selectById(param.getCloudLocationGroupId());
        if (cloudLocationGroup == null){
            throw new KylinBusinessException(500, "分组信息不存在");
        }
        if (!Objects.equals(cloudLocationGroup.getCloudApplicationId(), param.getCloudApplicationId())){
            throw new KylinBusinessException(500, "分组不在当前应用下");
        }
        if (device == null) {
            // 新设备
            device = new CloudDevice();
            BeanUtils.copyProperties(param, device);
            device.setCreateTime(new Date());
            device.setCloudLocationId(cloudLocationGroup.getLocationId());
            device.setCloudLocationGroupId(cloudLocationGroup.getId());
            cloudDeviceMapper.insert(device);
        } else {
            if (device.getCloudApplicationId() == null) {
                device.setCloudApplicationId(param.getCloudApplicationId());
                device.setCreateTime(new Date());
                device.setCloudLocationId(cloudLocationGroup.getLocationId());
                device.setCloudLocationGroupId(cloudLocationGroup.getId());
                cloudDeviceMapper.updateById(device);
            } else {
//                if (!device.getCloudLocationId().equals(param.getCloudLocationId())) {
                    throw new KylinBusinessException(500, "该设备当前绑定在其他应用下，请先从其他应用移除后再添加");
//                }
            }
        }
    }

    public void enableButtonEmergencySwitch(String deviceSerial){
        int i = 1;
        boolean needLoop = true;
        while (needLoop && i < 5){
            JSONObject res = helpButtonClient.setEmergencySwitch(ysService.getTokenFromCache(), deviceSerial, true);
            if (res!= null){
                JSONObject meta = res.getJSONObject("meta");
                if (meta != null && meta.getInteger("code") != null && 200 == meta.getInteger("code")){
                    needLoop = false;
                }
            }
            i++;
        }
    }

    public void updateDevice(CloudDeviceParam param){
        CloudDevice device = cloudDeviceMapper.selectOne(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getDeviceSerial, param.getDeviceSerial())
                .eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId()));
        if (device == null){
            throw new KylinBusinessException(500, "设备信息不存在");
        }
        if (param.getCloudLocationId() != null) {
            device.setCloudLocationId(param.getCloudLocationId());
        }
        if (param.getCloudLocationGroupId() != null) {
            device.setCloudLocationGroupId(param.getCloudLocationGroupId());
        }
        if (StringUtils.isNotBlank(param.getDeviceName()) && !param.getDeviceName().equals(device.getDeviceName())){
            device.setDeviceName(param.getDeviceName());
        }
        cloudDeviceMapper.updateById(device);
    }

    public List<CloudDeviceDTO> queryList(CloudDeviceParam param){
        List<CloudDeviceDTO> result = new ArrayList<>();
        List<CloudDevice> cloudDevices = null;
        if (param.getCloudLocationGroupId() == 0){
            cloudDevices = cloudDeviceMapper.selectList(new QueryWrapper<CloudDevice>()
                    .lambda()
                    .eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId())
                    .isNull(CloudDevice::getCloudLocationGroupId)
                    .orderByDesc(CloudDevice::getId));
        }else {
            cloudDevices = cloudDeviceMapper.selectList(new QueryWrapper<CloudDevice>()
                    .lambda()
                    .eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId())
                    .eq(CloudDevice::getCloudLocationGroupId, param.getCloudLocationGroupId())
                    .orderByDesc(CloudDevice::getId));
        }
        if (CollectionUtils.isNotEmpty(cloudDevices)){
            result = JSONObject.parseArray(JSONObject.toJSONString(cloudDevices), CloudDeviceDTO.class);
        }
        return result;
    }


    public Page<CloudDeviceDTO> queryPage(CloudDeviceParam param){
        Page<CloudDevice> page = new Page<>(param.getPageNo(), param.getPageSize());
        Page<CloudDeviceDTO> res = cloudDeviceMapper.queryPage(page, param);
        if (CollectionUtils.isNotEmpty(res.getRecords())){
            List<String> modelList = res.getRecords().stream().map(CloudDeviceDTO::getModel).distinct().toList();
            List<DeviceVariety> deviceVarieties = deviceVarietyMapper.selectList(new QueryWrapper<DeviceVariety>()
                    .lambda().in(DeviceVariety::getModel, modelList));
            Map<String, String> varietyPicMap = new HashMap<>();
            deviceVarieties.forEach(c->varietyPicMap.put(c.getModel(), c.getPicUrl()));
            for (CloudDeviceDTO record : res.getRecords()) {
                record.setPicUrl(varietyPicMap.get(record.getModel()));
            }
        }
        return res;
    }

    public CloudDeviceDTO queryDetail(CloudDeviceParam param){
        CloudDeviceDTO result = new CloudDeviceDTO();
        CloudDevice cloudDevice = cloudDeviceMapper.selectById(param.getId());
        if (cloudDevice == null){
            throw new KylinBusinessException(500, "设备不存在");
        }
        BeanUtils.copyProperties(cloudDevice, result);
        return result;
    }

    public void setCsepSdnl1Config(CloudDeviceParam param){
        //睡眠伴侣  设置睡眠监测时间
        HashMap mapTime=new HashMap();
        mapTime.put("EndTime",param.getEndTime());
        mapTime.put("StartTime",param.getStartTime());
        //设置睡眠监测时间段
        sleepSDNL1Client.prop(ysService.getTokenFromCache(), param.getDeviceSerial(), 0, "global", "SleepDetector", "SleepDetectTimeRange", "PUT", JSON.toJSONString(mapTime));
        //设置离床未归时长
        sleepSDNL1Client.prop(ysService.getTokenFromCache(), param.getDeviceSerial(), 0, "global", "SleepDetector", "LeaveBedDuration", "PUT", Convert.toStr(param.getOutBedTime()));
    }

    public void setByzpSmldConfig(CloudDeviceParam param){
        String startTime = param.getStartTime();
        String endTime = param.getEndTime();
        int startTimeSeconds = LocalTime.parse(startTime).toSecondOfDay();
        int endTimeSeconds = LocalTime.parse(endTime).toSecondOfDay();
        JSONObject msg = new JSONObject();
        msg.put("msgType","config_SleepReport");
        JSONObject payloadObject = new JSONObject();
        payloadObject.put("starttime", startTimeSeconds);
        payloadObject.put("endtime", endTimeSeconds);
        msg.put("payload",payloadObject);
        mqttUtil.sendMsgToDevice(param.getDeviceSerial(), msg.toJSONString());
    }

    public void configDiaper(CloudDeviceParam param){
        JSONObject msg = new JSONObject();
        msg.put("msgType","config_diaper");
//        JSONObject payloadObject = new JSONObject();
        msg.put("blemac", param.getDeviceSerial());
//        msg.put("payload",payloadObject);
        mqttUtil.sendMsgToDevice(param.getRelDeviceSerial(), msg.toJSONString());
    }

    public void subscribeBatchTopic(CloudDeviceParam param){
        CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
        JSONObject msg = new JSONObject();
        msg.put("msgType","setDeviceFamily");
        msg.put("payload", cloudApplication.getAppId());
        mqttUtil.sendMsgToDevice(param.getDeviceSerial(), msg.toJSONString());
    }

    public void deleteDevice(CloudDeviceParam param){
        CloudDevice device = cloudDeviceMapper.selectOne(new QueryWrapper<CloudDevice>()
                .lambda().eq(CloudDevice::getDeviceSerial, param.getDeviceSerial())
                .eq(CloudDevice::getCloudApplicationId, param.getCloudApplicationId()));
        if (device == null){
            throw new KylinBusinessException(500, "设备信息不存在");
        }
        DeviceVariety deviceVariety = deviceVarietyMapper.selectOne(new QueryWrapper<DeviceVariety>()
                .lambda().eq(DeviceVariety::getModel, device.getModel()));
        if (deviceVariety == null){
            throw new KylinBusinessException(500, "设备型号不存在");
        }
        String deviceSerial = param.getDeviceSerial();
        cloudDeviceMapper.delete(new QueryWrapper<CloudDevice>().lambda().eq(CloudDevice::getDeviceSerial, deviceSerial));

        if ("萤石".equals(deviceVariety.getBrand())){
            if (param.getDeviceSerial().contains("-")){
                String gateWaySerial = deviceSerial.split("-")[0];
                String childDeviceSerial = deviceSerial.split("-")[1];
                List<ChildDeviceParam> listParam = new ArrayList<>();
                ChildDeviceParam childDevice = new ChildDeviceParam();
                childDevice.setChildDeviceSerial(childDeviceSerial);
                listParam.add(childDevice);
                JSONObject res = deviceGatewayClient.unlinkChildDevices(ysService.getTokenFromCache(), gateWaySerial, listParam);
                log.info("解绑子设备结果:" + res);
            }else {
                JSONObject res = ysClient.deleteDevice(ysService.getTokenFromCache(), param.getDeviceSerial());
                log.info("删除设备结果:" + res);
            }
        }
    }

    public void remoteOTA(String deviceSerial, String appId, String deviceModel){
        JSONObject msg = new JSONObject();
        msg.put("msgType", "OTA");
        String key = null;
        if (StringUtils.isNotBlank(deviceSerial)){
            key = deviceSerial;
        }else if (StringUtils.isNotBlank(appId) && StringUtils.isNotBlank(deviceModel)){
            key = appId + "/" + deviceModel;
        }
        mqttUtil.sendMsgToDevice(key, msg.toJSONString());
    }

    public void remoteOTA(OtaParam param){
        JSONObject msg = new JSONObject();
        msg.put("msgType", "OTA");
        String key = null;
        if (StringUtils.isNotBlank(param.getDeviceSerial())){
            key = param.getDeviceSerial();
        }else if (StringUtils.isNotBlank(param.getDeviceModel())){
            CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
            if (cloudApplication == null || StringUtils.isBlank(cloudApplication.getAppId())){
                throw new KylinBusinessException(500, "未找到有效的应用信息");
            }
            key = cloudApplication.getAppId() + "/" + param.getDeviceModel();
        }
        mqttUtil.sendMsgToDevice(key, msg.toJSONString());
    }

    public List<CloudDeviceDTO> queryListForDiaper(Long cloudApplicationId, String deviceSerial){
        String model = "BYZP-SMLD";
        List<CloudDeviceDTO> cloudDeviceDTOS = cloudDeviceMapper.queryListForDiaper(cloudApplicationId, deviceSerial, model);
        if (CollectionUtils.isNotEmpty(cloudDeviceDTOS)) {
            DeviceVariety deviceVariety = deviceVarietyMapper.selectOne(new QueryWrapper<DeviceVariety>().lambda().eq(DeviceVariety::getModel, model));
            cloudDeviceDTOS.forEach(c->c.setPicUrl(deviceVariety.getPicUrl()));
            return cloudDeviceDTOS;
        }
        return null;
    }

    public void configSmldWarn(SmldWarnParam param){
        JSONObject msg = new JSONObject();
        msg.put("msgType", "config_leidawarn");
        JSONObject payload = new JSONObject();
        payload.put("lc_warn_time", param.getLcWarnTime());
        payload.put("hx_warn_max", param.getHxWarnMax());
        payload.put("hx_warn_min", param.getHxWarnMin());
        payload.put("xl_warn_max", param.getXlWarnMax());
        payload.put("xl_warn_min", param.getXlWarnMin());
        msg.put("payload", payload);
        String key = null;
        if (StringUtils.isNotBlank(param.getDeviceSerial())){
            key = param.getDeviceSerial();
        }else if (StringUtils.isNotBlank(param.getDeviceModel())){
            CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
            if (cloudApplication == null || StringUtils.isBlank(cloudApplication.getAppId())){
                throw new KylinBusinessException(500, "未找到有效的应用信息");
            }
            key = cloudApplication.getAppId() + "/" + param.getDeviceModel();
        }
        mqttUtil.sendMsgToDevice(key, msg.toJSONString());
    }

    public void configSmldTime(SmldTimeParam param){
        String startTime = param.getStartTime();
        String endTime = param.getEndTime();
        int startTimeSeconds = LocalTime.parse(startTime).toSecondOfDay();
        int endTimeSeconds = LocalTime.parse(endTime).toSecondOfDay();
        JSONObject msg = new JSONObject();
        msg.put("msgType","config_SleepReport");
        JSONObject payloadObject = new JSONObject();
        payloadObject.put("starttime", startTimeSeconds);
        payloadObject.put("endtime", endTimeSeconds);
        msg.put("payload",payloadObject);
        String key = null;
        if (StringUtils.isNotBlank(param.getDeviceSerial())){
            key = param.getDeviceSerial();
        }else if (StringUtils.isNotBlank(param.getDeviceModel())){
            CloudApplication cloudApplication = cloudApplicationMapper.selectById(param.getCloudApplicationId());
            if (cloudApplication == null || StringUtils.isBlank(cloudApplication.getAppId())){
                throw new KylinBusinessException(500, "未找到有效的应用信息");
            }
            key = cloudApplication.getAppId() + "/" + param.getDeviceModel();
        }
        mqttUtil.sendMsgToDevice(key, msg.toJSONString());
    }

    public void recordVoice(RecordVoiceParam param){
        JSONObject msg = new JSONObject();
        msg.put("msgType","Recording_input");
        JSONObject payloadObject = new JSONObject();
        payloadObject.put("identification_ID", param.getVoiceId());
        payloadObject.put("cloudApplicationId", param.getCloudApplicationId());
        msg.put("payload",payloadObject);
        msg.put("clientId", param.getDeviceSerial());
        mqttUtil.sendMsgToDevice(param.getDeviceSerial(), msg.toJSONString());
    }

    public DeviceConfigDTO queryDeviceConfig(String deviceSerial){
        try {
            // 创建等待请求
            CompletableFuture<DeviceConfigDTO> future = deviceConfigWaitManager.createWaitingRequest(deviceSerial);

            // 发送MQTT消息到设备
            JSONObject msg = new JSONObject();
            msg.put("msgType","read_config");
            msg.put("clientId", deviceSerial);
            mqttUtil.sendMsgToDevice(deviceSerial, msg.toJSONString());

            // 等待设备响应，最多等待30秒
            return future.get();
        } catch (Exception e) {
            log.error("查询设备配置失败，deviceSerial: {}", deviceSerial, e);
            // 确保清理等待请求
            deviceConfigWaitManager.cancelRequest(deviceSerial);
            throw new KylinBusinessException(500, "查询设备配置失败: " + e.getMessage());
        }
    }
}
