package com.byzp.platform.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudDeviceDTO;
import com.byzp.platform.dto.CloudInkInfoDTO;
import com.byzp.platform.dto.CloudUserDTO;
import com.byzp.platform.param.CloudDeviceParam;
import com.byzp.platform.param.CloudInkInfoParam;
import com.byzp.platform.param.CloudLocationParam;
import com.byzp.platform.service.CloudInkInfoServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 云平台墨水屏
 */
@RequestMapping(value = "/cloudInkInfo")
@RestController
@CrossOrigin
@Slf4j
public class CloudInkInfoController {
    @Resource
    private CloudInkInfoServiceImpl cloudInkInfoService;

    /**
     * 分页列表
     *
     * @param user
     * @param param
     * @return
     */
    @GetMapping("/queryPage")
    public Page<CloudInkInfoDTO> queryPage(CloudUserDTO user, @ModelAttribute CloudInkInfoParam param){
        return cloudInkInfoService.queryPage(param);
    }


    /**
     * 新增或编辑墨水屏信息
     * @param param
     */
    @PostMapping("/save")
    public void save(@RequestBody CloudInkInfoParam param){
        cloudInkInfoService.save(param);
    }

    /**
     * 删除
     * @param param
     */
    @PostMapping("/delete")
    public void delete(@RequestBody CloudInkInfoParam param){
        cloudInkInfoService.delete(param);
    }

    /**
     * 前端回显墨水屏信息
     * @param id
     * @return
     */
    @GetMapping("/querySource")
    public String querySource(@RequestParam Long id,
                              @RequestParam Long cloudApplicationId){
        return cloudInkInfoService.querySource(id, cloudApplicationId);
    }

    /**
     * 查询墨水屏关联的设备列表
     * @param id
     * @return
     */
    @GetMapping("/refDeviceList")
    public CloudInkInfoDTO refDeviceList(@RequestParam Long id,
                                         @RequestParam Long cloudApplicationId){
        return cloudInkInfoService.refDeviceList(id, cloudApplicationId);
    }

    /**
     * 修改绑定的设备列表
     * @param param
     */
    @PostMapping("/updateRefDeviceList")
    public void updateRefDeviceList(@RequestBody CloudInkInfoParam param){
        cloudInkInfoService.updateRefDeviceList(param);
    }

    @GetMapping("/sendTest")
    public void sendTest(@RequestParam String deviceSerial,
                         @RequestParam Long id){
        cloudInkInfoService.sendTest(deviceSerial, id);
    }
}
