package com.byzp.platform.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudLocationGroupDTO;
import com.byzp.platform.param.CloudLocationGroupParam;
import com.byzp.platform.service.CloudLocationGroupServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 位置分组接口
 */
@RequestMapping(value = "/cloudLocationGroup")
@RestController
@CrossOrigin
@Slf4j
public class CloudLocationGroupController {

    @Resource
    private CloudLocationGroupServiceImpl cloudLocationGroupService;

    /**
     * 新增或修改位置分组信息
     * @param param
     */
    @PostMapping("/addOrUpdateForUser")
    public void addOrUpdateForUser(@RequestBody CloudLocationGroupParam param){
        cloudLocationGroupService.addOrUpdateForUser(param);
    }

    /**
     * 删除位置分组信息
     * @param param
     */
    @PostMapping("/delete")
    public void delete(@RequestBody CloudLocationGroupParam param){
        cloudLocationGroupService.delete(param);
    }

    /**
     * 分页列表
     * @param param
     * @return
     */
    @GetMapping("/queryPage")
    public Page<CloudLocationGroupDTO> queryPage(@ModelAttribute CloudLocationGroupParam param){
        return cloudLocationGroupService.queryPage(param);
    }
}
