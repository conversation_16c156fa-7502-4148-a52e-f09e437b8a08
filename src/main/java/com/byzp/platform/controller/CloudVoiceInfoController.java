package com.byzp.platform.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudVoiceInfoDTO;
import com.byzp.platform.param.CloudVoiceInfoParam;
import com.byzp.platform.service.VoiceInfoServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 声纹管理
 */
@RequestMapping(value = "/voiceManager")
@RestController
@CrossOrigin
@Slf4j
public class CloudVoiceInfoController {

    @Resource
    private VoiceInfoServiceImpl voiceInfoService;

    /**
     * 添加声纹信息
     * @param param
     */
    @PostMapping("/addVoice")
    public void addVoice(@RequestBody CloudVoiceInfoParam param){
        voiceInfoService.saveVoice(param);
    }

    /**
     * 分页列表
     * @param param
     * @return
     */
    @GetMapping("/queryPage")
    public Page<CloudVoiceInfoDTO> queryPage(@ModelAttribute CloudVoiceInfoParam param){
        return voiceInfoService.queryPage(param);
    }

    /**
     * 更新声纹
     * @param param
     */
    @PostMapping("/updateVoice")
    public void updateVoice(@RequestBody CloudVoiceInfoParam param){
        voiceInfoService.updateVoice(param);
    }

    /**
     * 删除声纹
     * @param param
     */
    @PostMapping("/deleteVoice")
    public void deleteVoice(@RequestBody CloudVoiceInfoParam param){
        voiceInfoService.deleteVoice(param);
    }
}
