package com.byzp.platform.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudLocationDTO;
import com.byzp.platform.dto.CloudUserDTO;
import com.byzp.platform.param.CloudLocationParam;
import com.byzp.platform.service.CloudLocationServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 位置接口
 */
@RequestMapping(value = "/cloudLocation")
@RestController
@CrossOrigin
@Slf4j
public class CloudLocationController {

    @Resource
    private CloudLocationServiceImpl cloudLocationService;

    /**
     * 新增或编辑位置信息
     * @param param
     */
    @PostMapping("/addOrUpdateForUser")
    public void addOrUpdateForUser(@RequestBody CloudLocationParam param){
        cloudLocationService.addOrUpdateForUser(param);
    }

    /**
     * 删除位置信息
     * @param param
     */
    @PostMapping("/delete")
    public void delete(@RequestBody CloudLocationParam param){
        cloudLocationService.delete(param);
    }

    /**
     * 查询位置下拉列表
     * @param param
     */
    @PostMapping("/queryList")
    public List<CloudLocationDTO> queryList(@ModelAttribute CloudLocationParam param){
        return cloudLocationService.queryList(param);
    }

    /**
     * 分页列表
     * @param user
     * @param param
     * @return
     */
    @GetMapping("/queryPage")
    public Page<CloudLocationDTO> queryPage(CloudUserDTO user, @ModelAttribute CloudLocationParam param){
        param.setCloudUserId(user.getId());
        return cloudLocationService.queryPage(param);
    }



}
