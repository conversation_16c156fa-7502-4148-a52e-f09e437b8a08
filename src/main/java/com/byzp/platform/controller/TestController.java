package com.byzp.platform.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.CallbackClient;
import com.byzp.platform.utils.AudioConverter;
import com.byzp.platform.utils.MqttUtil;
import com.byzp.platform.utils.kdxf.KdxfUtil;
import com.byzp.platform.utils.kdxf.SparkMultiIatModel;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.WebSocket;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.time.LocalTime;

import static com.byzp.platform.utils.AudioConverter.audioFileToBase64;
import static com.byzp.platform.utils.AudioConverter.decodeBase64ToByteArray;

@RequestMapping(value = "/test")
@RestController
@CrossOrigin
@Slf4j
public class TestController {
    @Resource
    private CallbackClient callbackClient;
    @Resource
    private MqttUtil mqttUtil;

    /**
     *
     * @param param
     */
    @PostMapping("/receive")
    public void receive(@RequestBody JSONObject param) throws Exception{

        // 构建鉴权url
//        String authUrl = KdxfUtil.getAuthUrl();
//        OkHttpClient client = new OkHttpClient.Builder().build();
//        //将url中的 schema http://和https://分别替换为ws:// 和 wss://
//        String url = authUrl.toString().replace("http://", "ws://").replace("https://", "wss://");
//        //log.info(url);
//        Request request = new Request.Builder().url(url).build();
//        // log.info(client.newCall(request).execute());
//        //log.info("url===>" + url);
//        String filePath = "D:\\spark_mucl_cn_iat_demo_java\\resource\\iat\\76633606-628.mp3";
////        String targetPath = "D:\\spark_mucl_cn_iat_demo_java\\resource\\iat\\copy.pcm";
//        String targetPath = "D:\\audio\\f3aeecb049b7415888f82bf94af44aab.pcm";
////        String mp3Base64 = audioFileToBase64(filePath);
////        byte[] pcmBytes = AudioConverter.convertMp3ToPcm(mp3Base64);
////        byte[] pcmBytes = convertMp3FileToPcm(filePath);
////        String originBase64 = audioFileToBase64(targetPath);
//        AudioConverter.convertMp3Base64ToPcmFile(param.getString("video"), targetPath);
//        String originBase64 = audioFileToBase64(targetPath);
//        byte[] originBytes = decodeBase64ToByteArray(originBase64);//--这个originBytes没问题
////        byte[] pcmBytes = AudioConverter.getPcmBase64FromMp3Byte(originBytes);
////        convertMp3Base64ToPcmFile(originBase64, targetPath);
//        WebSocket webSocket = client.newWebSocket(request, new SparkMultiIatModel(originBytes,"CD6785455","","","","","pcmBytes", null,""));
////        System.out.println(param.toJSONString());
        KdxfUtil.searchFeatures("595a246c382a8238",param.getString("audio"));
    }

    @GetMapping("/send")
    public void send(@RequestParam String deviceSerial,
                     @RequestParam String startTime,
                     @RequestParam String endTime){
        int startTimeSeconds = LocalTime.parse(startTime).toSecondOfDay();
        int endTimeSeconds = LocalTime.parse(endTime).toSecondOfDay();
        JSONObject msg = new JSONObject();
        msg.put("msgType","config_SleepReport");
        JSONObject payloadObject = new JSONObject();
        payloadObject.put("starttime", startTimeSeconds);
        payloadObject.put("endtime", endTimeSeconds);
        msg.put("payload",payloadObject);
        mqttUtil.sendMsgToDevice(deviceSerial, msg.toJSONString());
//        callbackClient.callback("http://192.168.4.3:8001/test/receive",param);
//        mqttUtil.sendMsgToDevice("30eda02afb70", param.toJSONString());
    }

    @GetMapping("/sendAppId")
    public void send1(@RequestParam String deviceSerial,
                     @RequestParam String appId){

        JSONObject msg = new JSONObject();
        msg.put("msgType","setDeviceFamily");
        msg.put("payload", appId);
        mqttUtil.sendMsgToDevice(deviceSerial, msg.toJSONString());
    }

    @GetMapping("/sendOTA")
    public void sendOTA(@RequestParam String deviceSerial){
        JSONObject msg = new JSONObject();
        msg.put("msgType","OTA");
        mqttUtil.sendMsgToDevice(deviceSerial, msg.toJSONString());
    }
}
