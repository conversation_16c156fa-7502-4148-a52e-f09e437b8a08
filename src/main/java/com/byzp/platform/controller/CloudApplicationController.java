package com.byzp.platform.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudApplicationDTO;
import com.byzp.platform.dto.CloudUserDTO;
import com.byzp.platform.mapper.po.CloudApplication;
import com.byzp.platform.param.CloudApplicationParam;
import com.byzp.platform.service.CloudApplicationServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 云平台应用
 */
@RequestMapping(value = "/cloudApp")
@RestController
@CrossOrigin
@Slf4j
public class CloudApplicationController {

    @Resource
    private CloudApplicationServiceImpl cloudApplicationService;

    /**
     * 新增或编辑应用
     * @param dto
     * @param param
     */
    @PostMapping("/addOrUpdateApplication")
    public void addOrUpdateApplication(CloudUserDTO dto, @RequestBody CloudApplicationParam param){
        cloudApplicationService.addOrUpdateApplication(dto, param);
    }

    /**
     * 删除应用
     * @param dto
     * @param param
     */
    @PostMapping("/delete")
    public void delete(CloudUserDTO dto, @RequestBody CloudApplicationParam param){
        cloudApplicationService.delete(dto, param);
    }

    /**
     * 刷新secret
     * @param param
     */
    @PostMapping("/refreshSecret")
    public void refreshSecret(@RequestBody CloudApplicationParam param){
        cloudApplicationService.refreshSecret(param);
    }

    /**
     * 查询详情
     * @param applicationId
     * @return
     */
    @GetMapping("/queryDetail")
    public CloudApplication queryDetail(@RequestParam Long applicationId){
        return cloudApplicationService.queryDetail(applicationId);
    }

    /**
     * 查询分页
     * @param user
     * @param param
     * @return
     */
    @GetMapping("/queryPage")
    public Page<CloudApplicationDTO> queryPage(CloudUserDTO user, @ModelAttribute CloudApplicationParam param){
        param.setCloudUserId(user.getId());
        return cloudApplicationService.queryPage(param);
    }

}
