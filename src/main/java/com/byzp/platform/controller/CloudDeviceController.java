package com.byzp.platform.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudDeviceDTO;
import com.byzp.platform.dto.CloudUserDTO;
import com.byzp.platform.param.CloudDeviceParam;
import com.byzp.platform.service.CloudDeviceServiceImpl;
import com.byzp.platform.utils.MqttUtil;
import javazoom.jl.decoder.Bitstream;
import javazoom.jl.decoder.BitstreamException;
import javazoom.jl.decoder.Header;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalTime;
import java.util.List;

/**
 * 云平台设备
 */
@RequestMapping(value = "/cloudDevice")
@RestController
@CrossOrigin
@Slf4j
public class CloudDeviceController {
    @Resource
    private CloudDeviceServiceImpl cloudDeviceService;
    @Resource
    private MqttUtil mqttUtil;

    /**
     * 分页列表
     *
     * @param user
     * @param param
     * @return
     */
    @GetMapping("/queryPage")
    public Page<CloudDeviceDTO> queryPage(CloudUserDTO user, @ModelAttribute CloudDeviceParam param){
        param.setCloudUserId(user.getId());
        return cloudDeviceService.queryPage(param);
    }

    /**
     * 添加设备
     * @param param
     */
    @PostMapping("/add")
    public void add(@RequestBody CloudDeviceParam param){
        cloudDeviceService.addDevice(param);
    }

    /**
     * 查询可关联尿不湿的设备列表
     * @return
     */
    @GetMapping("/listForDiaper")
    public List<CloudDeviceDTO> queryListForDiaper(@RequestParam Long cloudApplicationId,
                                          @RequestParam(required = false) String deviceSerial){
        return cloudDeviceService.queryListForDiaper(cloudApplicationId, deviceSerial);
    }

    /**
     * 更新
     * @param param
     */
    @PostMapping("/updateDevice")
    public void updateDevice(@RequestBody CloudDeviceParam param){
        cloudDeviceService.updateDevice(param);
    }

    /**
     * 设备远程升级（可根据设备序列号单个升级，或者根据appId和型号批量升级）
     * @param deviceSerial 设备序列号
     * @param appId 应用的appId
     * @param deviceModel 设备型号
     */
    @GetMapping("/remoteOTA")
    public void remoteOTA(@RequestParam(required = false) String deviceSerial,
                          @RequestParam(required = false) String appId,
                          @RequestParam(required = false) String deviceModel){
        cloudDeviceService.remoteOTA(deviceSerial, appId, deviceModel);
    }

    /**
     * 配置睡眠报告监测时间段
     * @param deviceSerial 设备序列号
     * @param startTime 开始时间 20:00
     * @param endTime 结束时间 08:00
     */
    @GetMapping("/configSleepReport")
    public void send(@RequestParam String deviceSerial,
                     @RequestParam String startTime,
                     @RequestParam String endTime){
        int startTimeSeconds = LocalTime.parse(startTime).toSecondOfDay();
        int endTimeSeconds = LocalTime.parse(endTime).toSecondOfDay();
        JSONObject msg = new JSONObject();
        msg.put("msgType","config_SleepReport");
        JSONObject payloadObject = new JSONObject();
        payloadObject.put("starttime", startTimeSeconds);
        payloadObject.put("endtime", endTimeSeconds);
        msg.put("payload",payloadObject);
        mqttUtil.sendMsgToDevice(deviceSerial, msg.toJSONString());
    }



//    public static void main(String[] args) throws Throwable {
////        String audioFilePath = "D:\\audio\\f3aeecb049b7415888f82bf94af44aab.mp3";  // 请替换为实际的音频文件路径
//        String audioFilePath = "D:\\iat_model_zh_java_demo\\resource\\iat\\16k.wav";  // 请替换为实际的音频文件路径
//        FileInputStream fis = new FileInputStream(new File(audioFilePath));
//        Bitstream bitstream = new Bitstream(fis);
//        Header header;
//        while ((header = bitstream.readFrame()) != null) {
//            // 获取采样率
//            int sampleRate = header.frequency();
//            // 获取比特率
//            int bitrate = header.bitrate();
//            // 获取音频版本
//            int version = header.version();
//            // 获取声道数
//            int channels = header.mode() == Header.SINGLE_CHANNEL ? 1 : 2;
//
//            System.out.println("采样率: " + sampleRate + " Hz");
//            System.out.println("比特率: " + bitrate + " kbps");
//            System.out.println("音频版本: " + version);
//            System.out.println("声道数: " + channels);
//
//            // 这里只处理第一帧的信息，实际可根据需求处理多帧
//            bitstream.closeFrame();
//            break;
//        }
//    }
}
