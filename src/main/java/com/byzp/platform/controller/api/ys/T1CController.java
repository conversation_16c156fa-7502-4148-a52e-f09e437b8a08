package com.byzp.platform.controller.api.ys;


import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.ys.T1CClient;
import com.byzp.platform.service.ys.YsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * T1C人体移动传感器
 */
@RestController
@RequestMapping("/api/t1c")
@Slf4j
@CrossOrigin
public class T1CController {

    @Resource
    private T1CClient t1CClient;

    @Resource
    private YsServiceImpl ysService;


    /**
     * 查询pir状态
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectPirStatus")
    public JSONObject selectPirStatus(@RequestParam() String deviceSerial) {
        return t1CClient.selectPirStatus(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置pir状态
     *
     * @param deviceSerial 设备号
     * @param data         pir状态 1-有人移动 2-正常
     * @return
     */
    @GetMapping("/setPirStatus")
    public JSONObject setPirStatus(@RequestParam() String deviceSerial, @RequestParam() Integer data) {
        return t1CClient.setPirStatus(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询提醒开关
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectRemindSwitch")
    public JSONObject selectRemindSwitch(@RequestParam() String deviceSerial) {
        return t1CClient.selectRemindSwitch(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置提醒开关
     *
     * @param deviceSerial 设备号
     * @param data         提醒开关
     * @return
     */
    @GetMapping("/setRemindSwitch")
    public JSONObject setRemindSwitch(@RequestParam() String deviceSerial, @RequestParam() Boolean data) {
        return t1CClient.setRemindSwitch(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询运行模式
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectRunningMode")
    public JSONObject selectRunningMode(@RequestParam() String deviceSerial) {
        return t1CClient.selectRunningMode(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置运行模式
     *
     * @param deviceSerial 设备号
     * @param data         设备模式	  EnergySaving-节能模式  Smart-智能家居模式
     * @return
     */
    @GetMapping("/setRunningMode")
    public JSONObject setRunningMode(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return t1CClient.setRunningMode(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 一键消警
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/erasure")
    public JSONObject erasure(@RequestParam() String deviceSerial) {
        return t1CClient.erasure(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 查询提示音音量
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectPromptToneVolume")
    public JSONObject selectPromptToneVolume(@RequestParam() String deviceSerial) {
        return t1CClient.selectPromptToneVolume(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置提示音音量
     *
     * @param deviceSerial 设备号
     * @param data         提示音音量
     * @return
     */
    @GetMapping("/setPromptToneVolume")
    public JSONObject setPromptToneVolume(@RequestParam() String deviceSerial, @RequestParam() Integer data) {
        return t1CClient.setPromptToneVolume(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询提醒使能开关
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectRemindEnabled")
    public JSONObject selectRemindEnabled(@RequestParam() String deviceSerial) {
        return t1CClient.selectRemindEnabled(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置提醒使能开关
     *
     * @param deviceSerial 设备号
     * @param data         提醒使能开关
     * @return
     */
    @GetMapping("/setRemindEnabled")
    public JSONObject setRemindEnabled(@RequestParam() String deviceSerial, @RequestParam() Boolean data) {
        return t1CClient.setRemindEnabled(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询提示音类型
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectSoundTypes")
    public JSONObject selectSoundTypes(@RequestParam() String deviceSerial) {
        return t1CClient.selectSoundTypes(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置提示音类型
     *
     * @param deviceSerial 设备号
     * @param data         提示音类型 0-告警音 1-自定义语音
     * @return
     */
    @GetMapping("/setSoundTypes")
    public JSONObject setSoundTypes(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return t1CClient.setSoundTypes(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询自定义声音
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectCustomSound")
    public JSONObject selectCustomSound(@RequestParam() String deviceSerial) {
        return t1CClient.selectCustomSound(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置自定义声音
     *
     * @param deviceSerial 设备号
     * @param data         音效 1-叮咚  2-有人闯入  3-欢迎光临  4-请随时关门  5-请注意安全
     * @return
     */
    @GetMapping("/setCustomSound")
    public JSONObject setCustomSound(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return t1CClient.setCustomSound(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询告警声音使能开关
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectAlarmSoundEnabled")
    public JSONObject selectAlarmSoundEnabled(@RequestParam() String deviceSerial) {
        return t1CClient.selectAlarmSoundEnabled(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置告警声音使能开关
     *
     * @param deviceSerial 设备号
     * @param data         voiceIndex（integer） 自定义语音的索引  enabled（boolean）  声音开关  soundType（integer） 声音类型 0-短叫 1-长叫 2-静音 3-自定义语音
     * @return
     */
    @GetMapping("/setAlarmSoundEnabled")
    public JSONObject setAlarmSoundEnabled(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return t1CClient.setAlarmSoundEnabled(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 播放指定声音
     *
     * @param deviceSerial 设备号
     * @param data         volume（integer） 音量  playSoundType（string）  声音类型 alarm-告警音  call-呼叫音    index（integer） 声音索引
     * @return
     */
    @GetMapping("/setPlaySpecificSound")
    public JSONObject setPlaySpecificSound(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return t1CClient.setPlaySpecificSound(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询网关自定义声音
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectGatewayRemindCustomSound")
    public JSONObject selectGatewayRemindCustomSound(@RequestParam() String deviceSerial) {
        return t1CClient.selectGatewayRemindCustomSound(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置网关自定义声音
     *
     * @param deviceSerial 设备号
     * @param data         音效 1-叮咚 2-有人闯入 3-欢迎光临 4-请随时关门 5-请注意安全 20-微风 21-警告 22-叮 23-回声 24-退场 25-前进 26-激光 27-钢琴键 28-前奏 29-渐进 30-脉冲 31-叮咛 32-上课铃 33-激板 34-柔和 35-舒缓 36-滴答 37-按键音 38-振奋 39-圆舞曲
     * @return
     */
    @GetMapping("/setGatewayRemindCustomSound")
    public JSONObject setGatewayRemindCustomSound(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return t1CClient.setGatewayRemindCustomSound(ysService.getTokenFromCache(), deviceSerial, data);
    }

    /**
     * 查询网关提醒开关
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectGateWayRemindSwitch")
    public JSONObject selectGateWayRemindSwitch(@RequestParam() String deviceSerial) {
        return t1CClient.selectGateWayRemindSwitch(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置网关提醒开关
     *
     * @param deviceSerial 设备号
     * @param data         网关提醒开关
     * @return
     */
    @GetMapping("/setGateWayRemindSwitch")
    public JSONObject setGateWayRemindSwitch(@RequestParam() String deviceSerial, @RequestParam() Boolean data) {
        return t1CClient.setGateWayRemindSwitch(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询网关提示音类型
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectGatewayRemindSoundTypes")
    public JSONObject selectGatewayRemindSoundTypes(@RequestParam() String deviceSerial) {
        return t1CClient.selectGatewayRemindSoundTypes(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置网关提示音类型
     *
     * @param deviceSerial 设备号
     * @param data         提示音类型 0-告警音 1-自定义语音
     * @return
     */
    @GetMapping("/setGatewayRemindSoundTypes")
    public JSONObject setGatewayRemindSoundTypes(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return t1CClient.setGatewayRemindSoundTypes(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询网关声音提醒状态
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectSoundRemindStatus")
    public JSONObject selectSoundRemindStatus(@RequestParam() String deviceSerial) {
        return t1CClient.selectSoundRemindStatus(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置网关声音提醒状态
     *
     * @param deviceSerial 设备号
     * @param data         网关声音提醒状态
     * @return
     */
    @GetMapping("/setSoundRemindStatus")
    public JSONObject setSoundRemindStatus(@RequestParam() String deviceSerial, @RequestParam() Boolean data) {
        return t1CClient.setSoundRemindStatus(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询剩余电量
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectSurplusPower")
    public JSONObject selectSurplusPower(@RequestParam() String deviceSerial) {
        return t1CClient.selectSurplusPower(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置剩余电量
     *
     * @param deviceSerial 设备号
     * @param data         剩余电量
     * @return
     */
    @GetMapping("/setSurplusPower")
    public JSONObject setSurplusPower(@RequestParam() String deviceSerial, @RequestParam() Integer data) {
        return t1CClient.setSurplusPower(ysService.getTokenFromCache(), deviceSerial, data);
    }


}
