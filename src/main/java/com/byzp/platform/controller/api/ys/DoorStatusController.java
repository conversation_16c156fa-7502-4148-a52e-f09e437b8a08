package com.byzp.platform.controller.api.ys;


import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.ys.DoorStatusClient;
import com.byzp.platform.service.ys.YsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 智能门窗传感器(门磁)
 */
@RestController
@RequestMapping("/api/doorStatus")
@Slf4j
@CrossOrigin
public class DoorStatusController {

    @Resource
    private DoorStatusClient doorStatusClient;

    @Resource
    private YsServiceImpl ysService;


    /**
     * 查询工作状态
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectDoorStatus")
    public JSONObject selectDoorStatus(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectDoorStatus(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 设置工作状态
     *
     *  
     * @param deviceSerial 设备号
     * @param data         开关
     * @return
     */
    @GetMapping("/setDoorStatus")
    public JSONObject setDoorStatus(@RequestParam() String deviceSerial, @RequestParam() Boolean data) {
        return doorStatusClient.setDoorStatus(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询未闭合检测时间
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectOpenDetectionTime")
    public JSONObject selectOpenDetectionTime(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectOpenDetectionTime(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置未闭合检测时间
     *
     *  
     * @param deviceSerial 设备号
     * @param data         未闭合检测时间 0-关闭 1-1分钟 2-2分钟 5-5分钟
     * @return
     */
    @GetMapping("/setOpenDetectionTime")
    public JSONObject setOpenDetectionTime(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return doorStatusClient.setOpenDetectionTime(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询闭合提醒开关
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectClosedRemindSwitch")
    public JSONObject selectClosedRemindSwitch(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectClosedRemindSwitch(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置闭合提醒开关
     *
     *  
     * @param deviceSerial 设备号
     * @param data         闭合提醒开关
     * @return
     */
    @GetMapping("/setClosedRemindSwitch")
    public JSONObject setClosedRemindSwitch(@RequestParam() String deviceSerial, @RequestParam() Boolean data) {
        return doorStatusClient.setClosedRemindSwitch(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询闭合-网关提醒开关
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectClosedGateWaySwitch")
    public JSONObject selectClosedGateWaySwitch(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectClosedGateWaySwitch(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置闭合-网关提醒开关
     *
     *  
     * @param deviceSerial 设备号
     * @param data         闭合-网关提醒开关
     * @return
     */
    @GetMapping("/setClosedGateWaySwitch")
    public JSONObject setClosedGateWaySwitch(@RequestParam() String deviceSerial, @RequestParam() Boolean data) {
        return doorStatusClient.setClosedGateWaySwitch(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询闭合自定义声音类型
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectClosedCustomSoundTypes")
    public JSONObject selectClosedCustomSoundTypes(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectClosedCustomSoundTypes(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置闭合自定义声音类型
     *
     *  
     * @param deviceSerial 设备号
     * @param data         音效 1-叮咚 2-有人闯入 3-欢迎光临 4-请随时关门 5-请注意安全
     * @return
     */
    @GetMapping("/setClosedCustomSoundTypes")
    public JSONObject setClosedCustomSoundTypes(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return doorStatusClient.setClosedCustomSoundTypes(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询闭合-音效
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectClosedSound")
    public JSONObject selectClosedSound(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectClosedSound(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置闭合-音效
     *
     *  
     * @param deviceSerial 设备号
     * @param data         闭合-音效 0-告警音 1-自定义语音
     * @return
     */
    @GetMapping("/setClosedSound")
    public JSONObject setClosedSound(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return doorStatusClient.setClosedSound(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询未闭合提醒开关
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectOpenRemindSwitch")
    public JSONObject selectOpenRemindSwitch(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectOpenRemindSwitch(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置未闭合提醒开关
     *
     *  
     * @param deviceSerial 设备号
     * @param data         未闭合提醒开关
     * @return
     */
    @GetMapping("/setOpenRemindSwitch")
    public JSONObject setOpenRemindSwitch(@RequestParam() String deviceSerial, @RequestParam() Boolean data) {
        return doorStatusClient.setOpenRemindSwitch(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询未闭合-网关提醒开关
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectOpenGateWaySwitch")
    public JSONObject selectOpenGateWaySwitch(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectOpenGateWaySwitch(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置未闭合-网关提醒开关
     *
     *  
     * @param deviceSerial 设备号
     * @param data         未闭合-网关提醒开关
     * @return
     */
    @GetMapping("/setOpenGateWaySwitch")
    public JSONObject setOpenGateWaySwitch(@RequestParam() String deviceSerial, @RequestParam() Boolean data) {
        return doorStatusClient.setOpenGateWaySwitch(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询未闭合自定义声音类型
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectOpenCustomSoundTypes")
    public JSONObject selectOpenCustomSoundTypes(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectOpenCustomSoundTypes(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置未闭合自定义声音类型
     *
     *  
     * @param deviceSerial 设备号
     * @param data         音效  1-叮咚 2-有人闯入 3-欢迎光临 4-请随时关门 5-请注意安全
     * @return
     */
    @GetMapping("/setOpenCustomSoundTypes")
    public JSONObject setOpenCustomSoundTypes(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return doorStatusClient.setOpenCustomSoundTypes(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询未闭合音效
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectOpenSound")
    public JSONObject selectOpenSound(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectOpenSound(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置未闭合音效
     *
     *  
     * @param deviceSerial 设备号
     * @param data         未闭合音效  0-告警音 1-自定义语音
     * @return
     */
    @GetMapping("/setOpenSound")
    public JSONObject setOpenSound(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return doorStatusClient.setOpenSound(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 消音
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/erasure")
    public JSONObject erasure(@RequestParam() String deviceSerial) {
        return doorStatusClient.erasure(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 查询剩余电量
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectSurplusPower")
    public JSONObject selectSurplusPower(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectSurplusPower(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置剩余电量
     *
     *  
     * @param deviceSerial 设备号
     * @param data         剩余电量  0-100
     * @return
     */
    @GetMapping("/setSurplusPower")
    public JSONObject setSurplusPower(@RequestParam() String deviceSerial, @RequestParam() Integer data) {
        return doorStatusClient.setSurplusPower(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询提示音音量
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectPromptToneVolume")
    public JSONObject selectPromptToneVolume(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectPromptToneVolume(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置提示音音量
     *
     *  
     * @param deviceSerial 设备号
     * @param data         提示音音量
     * @return
     */
    @GetMapping("/setPromptToneVolume")
    public JSONObject setPromptToneVolume(@RequestParam() String deviceSerial, @RequestParam() Integer data) {
        return doorStatusClient.setPromptToneVolume(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询提醒使能开关
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectRemindEnabled")
    public JSONObject selectRemindEnabled(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectRemindEnabled(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置提醒使能开关
     *
     *  
     * @param deviceSerial 设备号
     * @param data         提醒使能开关
     * @return
     */
    @GetMapping("/setRemindEnabled")
    public JSONObject setRemindEnabled(@RequestParam() String deviceSerial, @RequestParam() Boolean data) {
        return doorStatusClient.setRemindEnabled(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询提示音类型
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectSoundTypes")
    public JSONObject selectSoundTypes(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectSoundTypes(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置提示音类型
     *
     *  
     * @param deviceSerial 设备号
     * @param data         提示音类型 0-告警音 1-自定义语音
     * @return
     */
    @GetMapping("/setSoundTypes")
    public JSONObject setSoundTypes(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return doorStatusClient.setSoundTypes(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询自定义声音
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectCustomSound")
    public JSONObject selectCustomSound(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectCustomSound(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置自定义声音
     *
     *  
     * @param deviceSerial 设备号
     * @param data         音效 1-叮咚 2-有人闯入 3-欢迎光临 4-请随时关门 5-请注意安全
     * @return
     */
    @GetMapping("/setCustomSound")
    public JSONObject setCustomSound(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return doorStatusClient.setCustomSound(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询告警声音使能开关
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/selectAlarmSoundEnabled")
    public JSONObject selectAlarmSoundEnabled(@RequestParam() String deviceSerial) {
        return doorStatusClient.selectAlarmSoundEnabled(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置告警声音使能开关
     *
     *  
     * @param deviceSerial 设备号
     * @param data         voiceIndex(integer) 自定义语音的索引  enabled(boolean) 声音开关  soundType(integer) 声音类型 0-短叫 1-长叫 2-静音 3-自定义语音
     * @return
     */
    @GetMapping("/setAlarmSoundEnabled")
    public JSONObject setAlarmSoundEnabled(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return doorStatusClient.setAlarmSoundEnabled(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 播放指定声音
     *
     *  
     * @param deviceSerial 设备号
     * @param data         volume(integer) 音量 0-100  playSoundType(String) 声音类型 alarm-告警音 call-呼叫音  index(integer) 声音索引
     * @return
     */
    @GetMapping("/playSpecificSound")
    public JSONObject playSpecificSound(@RequestParam() String deviceSerial, @RequestParam() String data) {
        return doorStatusClient.playSpecificSound(ysService.getTokenFromCache(), deviceSerial, data);
    }


}
