package com.byzp.platform.controller.api.ys;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.ys.HelpButtonClient;
import com.byzp.kylin.core.annotation.ApiVersion;
import com.byzp.platform.service.ys.YsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 智能按钮远程查询和设置接口
 */
@RestController
@ApiVersion(1)
@RequestMapping("/api/helpButton")
@CrossOrigin
@Slf4j
public class DeviceHelpButtonController {
    @Resource
    private HelpButtonClient helpButtonClient;
    @Resource
    private YsServiceImpl ysService;

    /**
     * 查询是否启用紧急按钮开关
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryEmergencySwitch")
    public JSONObject queryEmergencySwitch(@RequestParam String deviceSerial) {
        return helpButtonClient.queryEmergencySwitch(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置是否启用紧急按钮开关
     *
     * @param deviceSerial
     * @param enabled
     * @return
     */
    @GetMapping("/setEmergencySwitch")
    public JSONObject setEmergencySwitch(@RequestParam String deviceSerial, @RequestParam boolean enabled) {
        return helpButtonClient.setEmergencySwitch(ysService.getTokenFromCache(), deviceSerial, enabled);
    }

    /**
     * 一键消音
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/erasure")
    public JSONObject erasure(@RequestParam String deviceSerial) {
        return helpButtonClient.erasure(ysService.getTokenFromCache(), deviceSerial, "null");
    }

    /**
     * 查询剩余电量
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/querySurplusPower")
    public JSONObject querySurplusPower(@RequestParam String deviceSerial) {
        return helpButtonClient.querySurplusPower(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置剩余电量
     *
     * @param deviceSerial
     * @param power        电量 0-100
     * @return
     */
    @GetMapping("/setSurplusPower")
    public JSONObject setSurplusPower(@RequestParam String deviceSerial, @RequestParam Integer power) {
        return helpButtonClient.setSurplusPower(ysService.getTokenFromCache(), deviceSerial, power);
    }

    /**
     * 查询提示音音量
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryPromptToneVolume")
    public JSONObject queryPromptToneVolume(@RequestParam String deviceSerial) {
        return helpButtonClient.queryPromptToneVolume(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置提示音音量
     *
     * @param deviceSerial
     * @param volume       音量 0-100
     * @return
     */
    @GetMapping("/setPromptToneVolume")
    public JSONObject setPromptToneVolume(@RequestParam String deviceSerial, @RequestParam Integer volume) {
        return helpButtonClient.setPromptToneVolume(ysService.getTokenFromCache(), deviceSerial, volume);
    }

    /**
     * 查询提醒使能开关
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryRemindEnabled")
    public JSONObject queryRemindEnabled(@RequestParam String deviceSerial) {
        return helpButtonClient.queryRemindEnabled(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置提醒使能开关
     *
     * @param deviceSerial
     * @param enabled
     * @return
     */
    @GetMapping("/setRemindEnabled")
    public JSONObject setRemindEnabled(@RequestParam String deviceSerial, @RequestParam boolean enabled) {
        return helpButtonClient.setRemindEnabled(ysService.getTokenFromCache(), deviceSerial, enabled);
    }

    /**
     * 查询提示音类型
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/querySoundTypes")
    public JSONObject querySoundTypes(@RequestParam String deviceSerial) {
        return helpButtonClient.querySoundTypes(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置提示音类型
     *
     * @param deviceSerial
     * @param soundType    0-告警音，1-自定义语音
     * @return
     */
    @GetMapping("/setSoundTypes")
    public JSONObject setSoundTypes(@RequestParam String deviceSerial, @RequestParam int soundType) {
        return helpButtonClient.setSoundTypes(ysService.getTokenFromCache(), deviceSerial, soundType);
    }

    /**
     * 查询自定义声音
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryCustomSound")
    public JSONObject queryCustomSound(@RequestParam String deviceSerial) {
        return helpButtonClient.queryCustomSound(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置自定义声音
     *
     * @param deviceSerial
     * @param data         1-叮咚，2-有人闯入，3-欢迎光临，4-请随时关门，5-请注意安全
     * @return
     */
    @GetMapping("/setCustomSound")
    public JSONObject setCustomSound(@RequestParam String deviceSerial, @RequestParam String data) {
        return helpButtonClient.setCustomSound(ysService.getTokenFromCache(), deviceSerial, data);
    }

    /**
     * 查询告警声音使能开关
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryAlarmSoundEnabled")
    public JSONObject queryAlarmSoundEnabled(@RequestParam String deviceSerial) {
        return helpButtonClient.queryAlarmSoundEnabled(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置告警声音使能开关
     *
     * @param deviceSerial
     * @param enabled      声音开关
     * @param soundType    0-短叫，1-长叫，2-静音，3-自定义语音
     * @return
     */
    @GetMapping("/setAlarmSoundEnabled")
    public JSONObject setAlarmSoundEnabled(@RequestParam String deviceSerial, @RequestParam boolean enabled, @RequestParam int soundType) {
        return helpButtonClient.setAlarmSoundEnabled(ysService.getTokenFromCache(), deviceSerial, enabled, soundType);
    }

    /**
     * 播放指定声音
     *
     * @param deviceSerial
     * @param volume        音量 0-100
     * @param index         声音索引 0-65535
     * @param playSoundType alarm-告警音，call-呼叫音
     * @return
     */
    @GetMapping("/playSpecificSound")
    public JSONObject playSpecificSound(@RequestParam String deviceSerial, @RequestParam int volume, @RequestParam int index, @RequestParam String playSoundType) {
        return helpButtonClient.playSpecificSound(ysService.getTokenFromCache(), deviceSerial, volume, index, playSoundType);
    }

    /**
     * 查询网关自定义声音
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryGatewayCustomSound")
    public JSONObject queryGatewayCustomSound(@RequestParam String deviceSerial) {
        return helpButtonClient.queryGatewayCustomSound(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置网关自定义声音
     *
     * @param deviceSerial
     * @param data         1-叮咚
     *                     2-有人闯入
     *                     3-欢迎光临
     *                     4-请随时关门
     *                     5-请注意安全
     *                     20-微风
     *                     21-警告
     *                     22-叮
     *                     23-回声
     *                     24-退场
     *                     25-前进
     *                     26-激光
     *                     27-钢琴键
     *                     28-前奏
     *                     29-渐进
     *                     30-脉冲
     *                     31-叮咛
     *                     32-上课铃
     *                     33-激板
     *                     34-柔和
     *                     35-舒缓
     *                     36-滴答
     *                     37-按键音
     *                     38-振奋
     *                     39-圆舞曲
     * @return
     */
    @GetMapping("/setGatewayCustomSound")
    public JSONObject setGatewayCustomSound(@RequestParam String deviceSerial, @RequestParam String data) {
        return helpButtonClient.setGatewayCustomSound(ysService.getTokenFromCache(), deviceSerial, data);
    }

    /**
     * 查询网关提醒开关
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryGatewayRemindEnabled")
    public JSONObject queryGatewayRemindEnabled(@RequestParam String deviceSerial) {
        return helpButtonClient.queryGatewayRemindEnabled(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置网关提醒开关
     *
     * @param deviceSerial
     * @param enabled
     * @return
     */
    @GetMapping("/setGatewayRemindEnabled")
    public JSONObject setGatewayRemindEnabled(@RequestParam String deviceSerial, @RequestParam boolean enabled) {
        return helpButtonClient.setGatewayRemindEnabled(ysService.getTokenFromCache(), deviceSerial, enabled);
    }

    /**
     * 查询网关提示音类型
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryGatewaySoundTypes")
    public JSONObject queryGatewaySoundTypes(@RequestParam String deviceSerial) {
        return helpButtonClient.queryGatewaySoundTypes(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置网关提示音类型
     *
     * @param deviceSerial
     * @param soundType    0-告警音，1-自定义语音
     * @return
     */
    @GetMapping("/setGatewaySoundTypes")
    public JSONObject setGatewaySoundTypes(@RequestParam String deviceSerial, @RequestParam String soundType) {
        return helpButtonClient.setGatewaySoundTypes(ysService.getTokenFromCache(), deviceSerial, soundType);
    }

    /**
     * 查询网关声音提醒状态
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/querySoundRemindStatus")
    public JSONObject querySoundRemindStatus(@RequestParam String deviceSerial) {
        return helpButtonClient.querySoundRemindStatus(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置网关声音提醒状态
     *
     * @param deviceSerial
     * @param enabled
     * @return
     */
    @GetMapping("/setSoundRemindStatus")
    public JSONObject setSoundRemindStatus(@RequestParam String deviceSerial, @RequestParam boolean enabled) {
        return helpButtonClient.setSoundRemindStatus(ysService.getTokenFromCache(), deviceSerial, enabled);
    }
}
