package com.byzp.platform.controller.api.ys;

import com.alibaba.fastjson.JSONObject;
import com.byzp.kylin.core.annotation.ApiVersion;
import com.byzp.platform.client.ys.YsClient;
import com.byzp.platform.dto.ys.YsResult;
import com.byzp.platform.service.ys.YsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@ApiVersion(1)
@RequestMapping("/api/device")
@CrossOrigin
@Slf4j
public class DeviceController {


    @Resource
    private YsServiceImpl ysService;

    @Resource
    private YsClient ysClient;


    /**
     * 获取token
     * @return
     */
    @GetMapping("/getAccessToken")
    private String getAccessToken(){
        return ysService.getTokenFromCache();
    }


    /**
     * 设备上云
     * @param deviceSerial
     * @param validCode
     */
    @GetMapping("/add")
    public YsResult addDevice(@RequestParam String deviceSerial,
                              @RequestParam(required = false) String validCode){
        return ysClient.addDevice(ysService.getTokenFromCache(), deviceSerial, validCode);
    }


    /**
     * 自动确权
     */
    @GetMapping("/autoPermission")
    public JSONObject autoPermission(@RequestParam String deviceSerial){
        return ysService.autoPermission(deviceSerial);
    }

    /**
     * 离线确认
     */
    @GetMapping("/offlineConfirm")
    public JSONObject offlineConfirm(@RequestParam String deviceSerial){
        return ysService.offlineConfirm(deviceSerial);
    }

    /**
     * 在线确权
     */
    @GetMapping("/doPermission")
    public JSONObject doPermission(@RequestParam String deviceSerial){
        return ysService.doPermission(deviceSerial);
    }

    /**
     * 获取单个设备信息
     * @param deviceSerial
     * @return
     */
    @GetMapping("/singleDeviceInfo")
    public JSONObject singleDeviceInfo(@RequestParam String deviceSerial){
        return ysClient.singleDeviceInfo(ysService.getTokenFromCache(),deviceSerial);
    }

    /**
     * 设备基础信息查询
     * @param deviceSerial
     * @return
     */
    @GetMapping("/baseDeviceInfo")
    public JSONObject baseDeviceInfo(@RequestParam String deviceSerial){
        return ysService.baseDeviceInfo(deviceSerial);
    }


    /**
     * 删除设备信息
     * @param deviceSerial
     * @return
     */
    @GetMapping("/deleteDevice")
    public JSONObject deleteDevice(@RequestParam String deviceSerial){
        return ysClient.deleteDevice(ysService.getTokenFromCache(),deviceSerial);
    }


}
