package com.byzp.platform.controller.api.ys;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.ys.DeviceGatewayClient;
import com.byzp.platform.dto.ys.DeviceInfoParam;
import com.byzp.kylin.core.annotation.ApiVersion;
import com.byzp.platform.service.ys.YsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 智能网关远程查询和设置接口
 */
@RestController
@ApiVersion(1)
@RequestMapping("/api/gatewayDevice")
@CrossOrigin
@Slf4j
public class DeviceGatewayController {
    @Resource
    private YsServiceImpl ysService;
    @Resource
    private DeviceGatewayClient deviceGatewayClient;

    /**
     * 一键消警
     *
     * @param deviceSerial
     */
    @GetMapping("/cancelAlarm")
    public JSONObject cancelAlarm(@RequestParam String deviceSerial) {
        return deviceGatewayClient.cancelAlarm(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 关联子设备
     *
     * @param param
     * @return
     */
    @PostMapping("/linkChildDevices")
    public JSONObject linkChildDevices(@RequestBody DeviceInfoParam param) {
        return deviceGatewayClient.linkChildDevices(ysService.getTokenFromCache(), param.getDeviceSerial(), param.getDeviceSerial(), param.getChildDeviceList());
    }


    /**
     * 删除子设备(目前萤石只支持删除单个)
     *
     * @param param
     * @return
     */
    @PostMapping("/unlinkChildDevice")
    public JSONObject unlinkChildDevices(@RequestBody DeviceInfoParam param) {
        return deviceGatewayClient.unlinkChildDevices(ysService.getTokenFromCache(), param.getDeviceSerial(), param.getChildDeviceList());
    }

    /**
     * 网关下子设备列表
     *
     * @param deviceSerial
     * @return
     */
    @PostMapping("/childDeviceList")
    public JSONObject childDeviceList(@RequestParam String deviceSerial) {
        return deviceGatewayClient.childDeviceList(ysService.getTokenFromCache(), deviceSerial);
    }
}
