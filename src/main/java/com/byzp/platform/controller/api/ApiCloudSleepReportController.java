package com.byzp.platform.controller.api;

import com.byzp.platform.mapper.po.SleepReport;
import com.byzp.platform.param.SleepReportParam;
import com.byzp.platform.service.CloudSleepReportServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 睡眠报告接口
 */
@RequestMapping(value = "/api/sleepReport")
@RestController
@CrossOrigin
@Slf4j
public class ApiCloudSleepReportController {

    @Resource
    private CloudSleepReportServiceImpl cloudSleepReportService;

    /**
     * 根据设备序列号和日期，查询睡眠报告
     * @param param
     * @return
     */
    @PostMapping("/get")
    public SleepReport queryReport(@RequestBody SleepReportParam param){
        return cloudSleepReportService.queryReport(param.getDeviceSerial(), param.getReportDate());
    }
}
