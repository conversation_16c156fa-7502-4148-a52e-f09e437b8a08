package com.byzp.platform.controller.api;

import com.byzp.platform.dto.CloudLocationDTO;
import com.byzp.platform.param.CloudLocationParam;
import com.byzp.platform.service.CloudLocationServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 位置接口
 */
@RequestMapping(value = "/api/cloudLocation")
@RestController
@CrossOrigin
@Slf4j
public class ApiCloudLocationController {

    @Resource
    private CloudLocationServiceImpl cloudLocationService;

    /**
     * 新增或编辑位置信息
     * @param param
     */
    @PostMapping("/addOrUpdateForUser")
    public Long addOrUpdateForUser(@RequestBody CloudLocationParam param){
        return cloudLocationService.addOrUpdateForUser(param);
    }

    /**
     * 删除位置信息
     * @param param
     */
    @PostMapping("/delete")
    public void delete(@RequestBody CloudLocationParam param){
        cloudLocationService.delete(param);
    }

    /**
     * 查询位置下拉列表
     * @param param
     */
    @PostMapping("/queryList")
    public List<CloudLocationDTO> queryList(@RequestBody CloudLocationParam param){
        return cloudLocationService.queryList(param);
    }
}
