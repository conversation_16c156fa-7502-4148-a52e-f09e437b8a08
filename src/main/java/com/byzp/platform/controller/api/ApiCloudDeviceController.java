package com.byzp.platform.controller.api;

import com.byzp.platform.dto.CloudDeviceDTO;
import com.byzp.platform.dto.DeviceConfigDTO;
import com.byzp.platform.param.*;
import com.byzp.platform.service.CloudDeviceServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 云平台设备对外api
 */
@RequestMapping(value = "/api/cloudDevice")
@RestController
@CrossOrigin
@Slf4j
public class ApiCloudDeviceController {
    @Resource
    private CloudDeviceServiceImpl cloudDeviceService;

    /**
     * 添加设备
     * @param param
     */
    @PostMapping("/add")
    public void addDevice(@RequestBody CloudDeviceParam param){
        cloudDeviceService.addDevice(param);
    }

    /**
     * 更新设备信息
     * @param param
     */
    @PostMapping("/update")
    public void updateDevice(@RequestBody CloudDeviceParam param){
        cloudDeviceService.updateDevice(param);
    }

    /**
     * 查询设备列表
     * @param param
     */
    @PostMapping("/queryList")
    public List<CloudDeviceDTO> queryList(@RequestBody CloudDeviceParam param){
        return cloudDeviceService.queryList(param);
    }

    /**
     * 查询设备详情
     * @param param
     */
    @PostMapping("/detail")
    public CloudDeviceDTO queryDetail(@RequestBody CloudDeviceParam param){
        return cloudDeviceService.queryDetail(param);
    }

    /**
     * 修改萤石--睡眠伴侣配置
     * @param param
     */
    @PostMapping("/csepSdnl1Config")
    public void setCsepSdnl1Config(@RequestBody CloudDeviceParam param){
        cloudDeviceService.setCsepSdnl1Config(param);
    }

    /**
     * 删除设备
     * @param param
     */
    @PostMapping("/delete")
    public void delete(@RequestBody CloudDeviceParam param){
        cloudDeviceService.deleteDevice(param);
    }

    /**
     * 设备远程升级（可根据设备序列号单个升级，或者根据appId和型号批量升级）
     * @param param
     */
    @PostMapping("/remoteOTA")
    public void remoteOTA(@RequestBody OtaParam param){
        cloudDeviceService.remoteOTA(param);
    }


    /**
     * 设置睡眠雷达报警（可根据设备序列号单个设置，或者根据appId和型号批量设置）
     * @param param
     */
    @PostMapping("/configSmldWarn")
    public void configSmldWarn(@RequestBody SmldWarnParam param){
        cloudDeviceService.configSmldWarn(param);
    }

    /**
     * 设置睡眠雷达检测时间区间
     * @param param
     */
    @PostMapping("/configSmldTime")
    public void configSmldTime(@RequestBody SmldTimeParam param){
        cloudDeviceService.configSmldTime(param);
    }

    /**
     * 声纹录制
     * @param param
     */
    @PostMapping("/recordVoice")
    public void recordVoice(@RequestBody RecordVoiceParam param){
        cloudDeviceService.recordVoice(param);
    }

    /**
     * 查询设备配置信息
     * @param deviceSerial 设备序列号
     * @return
     */
    @GetMapping("/deviceInfo")
    public DeviceConfigDTO queryDeviceConfig(@RequestParam String deviceSerial){
        return cloudDeviceService.queryDeviceConfig(deviceSerial);
    }

}
