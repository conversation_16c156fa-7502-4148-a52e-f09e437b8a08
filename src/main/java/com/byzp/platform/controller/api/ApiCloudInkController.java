package com.byzp.platform.controller.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudInkInfoDTO;
import com.byzp.platform.dto.CloudUserDTO;
import com.byzp.platform.param.CloudInkInfoParam;
import com.byzp.platform.service.CloudInkInfoServiceImpl;
import com.byzp.platform.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 云平台墨水屏api
 */
@RequestMapping(value = "/api/cloudInk")
@RestController
@CrossOrigin
@Slf4j
public class ApiCloudInkController {
    @Resource
    private CloudInkInfoServiceImpl cloudInkInfoService;

    /**
     * 分页列表
     *
     * @param user
     * @param param
     * @return
     */
    @GetMapping("/queryPage")
    public Page<CloudInkInfoDTO> queryPage(CloudUserDTO user, @ModelAttribute CloudInkInfoParam param){
        return cloudInkInfoService.queryPage(param);
    }


    /**
     * 新增或编辑墨水屏信息
     * @param param
     */
    @PostMapping("/save")
    public void save(@RequestBody CloudInkInfoParam param){
        cloudInkInfoService.save(param);
    }

    /**
     * 删除
     * @param param
     */
    @PostMapping("/delete")
    public void delete(@RequestBody CloudInkInfoParam param){
        cloudInkInfoService.delete(param);
    }

    /**
     * 前端回显墨水屏信息
     * @param
     * @return
     */
    @GetMapping("/querySource")
    public String querySource(@RequestParam Long id){
        return cloudInkInfoService.querySource(id, null);
    }

    /**
     * 查询墨水屏关联的设备序列号列表
     * @id 墨水屏id
     * @return
     */
    @GetMapping("/refDeviceSerialList")
    public List<String> refDeviceList(@RequestParam Long id){
        return cloudInkInfoService.refDeviceSerialList(id, null);
    }

    /**
     * 修改绑定的设备列表
     * @param param
     */
    @PostMapping("/updateRefDeviceList")
    public void updateRefDeviceList(@RequestBody CloudInkInfoParam param){
        cloudInkInfoService.updateRefDeviceList(param);
    }

    /**
     * 给设备序列号列表中的设备发送墨水屏消息
     * @param param
     */
    @PostMapping("/sendContentToDevice")
    public void sendContentToDevice(@RequestBody CloudInkInfoParam param){
        cloudInkInfoService.sendContentToDevice(param);
    }

    public static void main(String[] args) {
        JSONArray arr = new JSONArray();
        JSONObject a1 = new JSONObject();
        a1.put("field", "name");
        a1.put("value","张三");
        arr.add(a1);
        JSONObject a2 = new JSONObject();
        a2.put("field", "age");
        a2.put("value","20");
        arr.add(a2);
        String mm = arr.toJSONString();
        System.out.println(mm);
    }
}
