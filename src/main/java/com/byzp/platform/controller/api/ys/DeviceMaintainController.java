package com.byzp.platform.controller.api.ys;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.ys.DeviceMaintainClient;
import com.byzp.kylin.core.annotation.ApiVersion;
import com.byzp.platform.service.ys.YsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 设备运维相关接口
 */
@RestController
@ApiVersion(1)
@RequestMapping("/api/maintain")
@CrossOrigin
@Slf4j
public class DeviceMaintainController {
    @Resource
    private DeviceMaintainClient deviceMaintainClient;
    @Resource
    private YsServiceImpl ysService;

    /**
     * 设置设备撤/布防
     *
     * @param deviceSerial
     * @param isDefence    具有防护能力设备布撤防状态：0-睡眠，8-在家，16-外出，普通IPC设备布撤防状态：`0-撤防，1-布防
     * @return
     */
    @GetMapping("/setDefence")
    public JSONObject setDefence(@RequestParam String deviceSerial, @RequestParam int isDefence) {
        return deviceMaintainClient.setDefence(ysService.getTokenFromCache(), deviceSerial, isDefence);
    }

    /**
     * 获取设备版本信息
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/versionInfo")
    public JSONObject queryVersionInfo(@RequestParam String deviceSerial) {
        return deviceMaintainClient.versionInfo(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设备升级固件
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/upgrade")
    public JSONObject upgradeVersion(@RequestParam String deviceSerial) {
        return deviceMaintainClient.upgrade(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 获取设备升级状态
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/upgradeStatus")
    public JSONObject upgradeVersionStatus(@RequestParam String deviceSerial) {
        return deviceMaintainClient.upgradeStatus(ysService.getTokenFromCache(), deviceSerial);
    }
}
