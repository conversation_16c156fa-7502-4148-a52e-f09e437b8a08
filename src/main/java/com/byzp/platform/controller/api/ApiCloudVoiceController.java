package com.byzp.platform.controller.api;

import com.byzp.platform.dto.CloudVoiceInfoDTO;
import com.byzp.platform.param.CloudVoiceInfoParam;
import com.byzp.platform.service.VoiceInfoServiceImpl;
import com.byzp.platform.utils.AuthUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 声纹管理
 */
@RequestMapping(value = "/api/voiceManager")
@RestController
@CrossOrigin
@Slf4j
public class ApiCloudVoiceController {

    @Resource
    private VoiceInfoServiceImpl voiceInfoService;

    /**
     * 添加/更新声纹信息
     * @param param
     */
    @PostMapping("/saveVoice")
    public void saveVoice(@RequestBody CloudVoiceInfoParam param){
        voiceInfoService.saveVoice(param);
    }

    /**
     * 更新声纹
     * @param param
     */
    @PostMapping("/updateVoice")
    public void updateVoice(@RequestBody CloudVoiceInfoParam param){
        voiceInfoService.updateVoice(param);
    }

    /**
     * 删除声纹
     * @param param
     */
    @PostMapping("/deleteVoice")
    public void deleteVoice(@RequestBody CloudVoiceInfoParam param){
        voiceInfoService.deleteVoice(param);
    }

    /**
     * 根据声纹id查询声纹内容
     * @param voiceId 声纹id
     */
    @GetMapping("/queryVoiceInfo")
    public CloudVoiceInfoDTO queryVoiceInfo(@RequestParam String voiceId){
        return voiceInfoService.queryVoiceInfo(voiceId, null);
    }
}
