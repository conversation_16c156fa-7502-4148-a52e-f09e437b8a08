package com.byzp.platform.controller.api;

import com.byzp.platform.param.CloudDeviceVoiceBindParam;
import com.byzp.platform.service.CloudDeviceVoiceBindServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 云平台设备与声纹绑定api
 */
@RequestMapping(value = "/api/cloudDeviceVoiceBind")
@RestController
@CrossOrigin
@Slf4j
public class ApiCloudDeviceVoiceBindController {
    @Resource
    private CloudDeviceVoiceBindServiceImpl cloudDeviceVoiceBindService;

    /**
     * 新增
     * @param param
     */
    @PostMapping("/insert")
    public void insertDeviceVoiceBind(@RequestBody CloudDeviceVoiceBindParam param){
        cloudDeviceVoiceBindService.insertDeviceVoiceBindV2(param);
    }


    /**
     * 删除
     * @param param
     */
    @PostMapping("/delete")
    public void deleteDeviceVoiceBind(@RequestBody CloudDeviceVoiceBindParam param){
        cloudDeviceVoiceBindService.deleteDeviceVoiceBind(param);
    }

    /**
     * 根据设备序列号查询声纹id列表
     * @param param
     */
    @PostMapping("/queryVoiceIdListByDeviceSerial")
    public List<String> queryVoiceIdListByDeviceSerial(@RequestBody CloudDeviceVoiceBindParam param){
        return cloudDeviceVoiceBindService.queryVoiceIdListByDeviceSerial(param);
    }

    /**
     * 单个绑定
     * @param param
     */
    @PostMapping("/bindOne")
    public void bindOne(@RequestBody CloudDeviceVoiceBindParam param){
        cloudDeviceVoiceBindService.bindOne(param);
    }

    /**
     * 单个解绑
     * @param param
     */
    @PostMapping("/unbindOne")
    public void unbindOne(@RequestBody CloudDeviceVoiceBindParam param){
        cloudDeviceVoiceBindService.unbindOne(param);
    }
}
