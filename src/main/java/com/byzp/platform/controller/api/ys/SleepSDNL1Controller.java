package com.byzp.platform.controller.api.ys;


import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.ys.SleepSDNL1Client;
import com.byzp.platform.service.ys.YsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;


/**
 * 睡眠伴侣CS-EP-SDNL1
 */
@RestController
@RequestMapping("/api/sleepSDNL1")
@Slf4j
@CrossOrigin
public class SleepSDNL1Controller {

    @Resource
    private SleepSDNL1Client sleepSDNL1Client;

    @Resource
    private YsServiceImpl ysService;


    /**
     * 睡眠伴侣的统一接口 根据传参 不同功能(具体看官方文档)
     *
     * @param deviceSerial 设备号
     * @return
     */
    @GetMapping("/prop")
    public JSONObject prop(@RequestParam() String deviceSerial, @RequestParam() Integer localIndex, @RequestParam() String resourceCategory, @RequestParam() String domainIdentifier, @RequestParam() String propIdentifier, @RequestParam() String type, @RequestBody String body) {
        return sleepSDNL1Client.prop(ysService.getTokenFromCache(), deviceSerial, localIndex, resourceCategory, domainIdentifier, propIdentifier, type, body);
    }


    /**
     * 森思泰克睡眠仪-获取睡眠报告（GET）
     *
     * @param startDate    开始日期，格式：yyyy-MM-dd
     * @param endDate      结束日期，格式：yyyy-MM-dd
     * @param deviceSerial 设备序列号
     * @param zipResponse  压缩报告结构体，去除图表分期数据
     * @return
     */
    @GetMapping("/sleepReportList")
    public JSONObject sleepReportList(@RequestParam("startDate") String startDate, @RequestParam("endDate") String endDate, @RequestParam("deviceSerial") String deviceSerial, @RequestParam("zipResponse") Boolean zipResponse

    ) {
        return sleepSDNL1Client.sleepReportList(ysService.getTokenFromCache(), startDate, endDate, deviceSerial, zipResponse);
    }


    /**
     * 获取设备在离床消息（GET）
     *
     * @param deviceSerial 设备序列号
     * @param offset       偏移量
     * @param limit        限制条数
     * @return
     */
    @GetMapping("/bodyDetect")
    public JSONObject bodyDetect(@RequestParam("deviceSerial") String deviceSerial, @RequestParam("endDate") Integer offset, @RequestParam("deviceSerial") Integer limit

    ) {
        return sleepSDNL1Client.bodyDetect(ysService.getTokenFromCache(), deviceSerial, offset, limit);
    }


}
