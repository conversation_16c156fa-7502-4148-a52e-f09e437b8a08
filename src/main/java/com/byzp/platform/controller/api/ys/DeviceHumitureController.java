package com.byzp.platform.controller.api.ys;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.ys.HumitureClient;
import com.byzp.kylin.core.annotation.ApiVersion;
import com.byzp.platform.service.ys.YsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 温湿度传感器远程查询和设置相关接口
 */
@RestController
@ApiVersion(1)
@RequestMapping("/api/humiture")
@CrossOrigin
@Slf4j
public class DeviceHumitureController {

    @Resource
    private HumitureClient humitureClient;
    @Resource
    private YsServiceImpl ysService;

    /**
     * 查询剩余电量
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/querySurplusPower")
    public JSONObject querySurplusPower(@RequestParam String deviceSerial) {
        return humitureClient.querySurplusPower(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置剩余电量
     *
     * @param deviceSerial
     * @param power        电量 0-100
     * @return
     */
    @GetMapping("/setSurplusPower")
    public JSONObject setSurplusPower(@RequestParam String deviceSerial, @RequestParam Integer power) {
        return humitureClient.setSurplusPower(ysService.getTokenFromCache(), deviceSerial, power);
    }

    /**
     * 查询湿度
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryHumidity")
    public JSONObject queryHumidity(@RequestParam String deviceSerial) {
        return humitureClient.queryHumidity(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置湿度
     *
     * @param deviceSerial
     * @param humidity     湿度 0-100
     * @return
     */
    @GetMapping("/setHumidity")
    public JSONObject setHumidity(@RequestParam String deviceSerial, @RequestParam Integer humidity) {
        return humitureClient.setHumidity(ysService.getTokenFromCache(), deviceSerial, humidity);
    }

    /**
     * 查询温度
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryTemperature")
    public JSONObject queryTemperature(@RequestParam String deviceSerial) {
        return humitureClient.queryTemperature(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置温度
     *
     * @param deviceSerial
     * @param temperature  湿度 -100.0-100000.0
     * @return
     */
    @GetMapping("/setTemperature")
    public JSONObject setTemperature(@RequestParam String deviceSerial, @RequestParam double temperature) {
        return humitureClient.setTemperature(ysService.getTokenFromCache(), deviceSerial, temperature);
    }

    /**
     * 查询测温单位
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryTemperatureUnit")
    public JSONObject queryTemperatureUnit(@RequestParam String deviceSerial) {
        return humitureClient.queryTemperatureUnit(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置测温单位
     *
     * @param deviceSerial
     * @param unit         单位： celsius-摄氏度，fahrenheit-华氏度
     * @return
     */
    @GetMapping("/setTemperatureUnit")
    public JSONObject setTemperatureUnit(@RequestParam String deviceSerial, @RequestParam String unit) {
        return humitureClient.setTemperatureUnit(ysService.getTokenFromCache(), deviceSerial, unit);
    }

    /**
     * 查询湿度告警阈值
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryHumidityAlarmThreshold")
    public JSONObject queryHumidityAlarmThreshold(@RequestParam String deviceSerial) {
        return humitureClient.queryHumidityAlarmThreshold(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置湿度告警阈值
     *
     * @param deviceSerial
     * @param enabled      是否开启湿度告警
     * @param maxHumidity  最大湿度 0-100
     * @param minHumidity  最小湿度 0-100
     * @return
     */
    @GetMapping("/setHumidityAlarmThreshold")
    public JSONObject setHumidityAlarmThreshold(@RequestParam String deviceSerial, @RequestParam boolean enabled, @RequestParam int minHumidity, @RequestParam int maxHumidity) {
        return humitureClient.setHumidityAlarmThreshold(ysService.getTokenFromCache(), deviceSerial, maxHumidity, minHumidity, enabled);
    }

    /**
     * 查询温度告警阈值
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryTemperatureAlarmThreshold")
    public JSONObject queryTemperatureAlarmThreshold(@RequestParam String deviceSerial) {
        return humitureClient.queryTemperatureAlarmThreshold(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置温度告警阈值
     *
     * @param deviceSerial
     * @param enabled        是否开启温度告警
     * @param maxTemperature 最大温度 -100.0-150
     * @param minTemperature 最小温度 -100.0-150
     * @param unit           单位：celsius-摄氏度, fahrenheit-华氏度
     * @return
     */
    @GetMapping("/setTemperatureAlarmThreshold")
    public JSONObject setTemperatureAlarmThreshold(@RequestParam String deviceSerial, @RequestParam boolean enabled, @RequestParam double minTemperature, @RequestParam double maxTemperature, @RequestParam String unit) {
        return humitureClient.setTemperatureAlarmThreshold(ysService.getTokenFromCache(), deviceSerial, minTemperature, maxTemperature, enabled, unit);
    }


    /**
     * 查询检测模式
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryDetectionMode")
    public JSONObject queryDetectionMode(@RequestParam String deviceSerial) {
        return humitureClient.queryDetectionMode(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置检测模式
     *
     * @param deviceSerial
     * @param mode         normal-正常模式, highPerformance-高性能模式
     * @param dataType     tempHumi-温湿度
     * @return
     */
    @GetMapping("/setDetectionMode")
    public JSONObject setDetectionMode(@RequestParam String deviceSerial, @RequestParam String mode, @RequestParam String dataType) {
        return humitureClient.setDetectionMode(ysService.getTokenFromCache(), deviceSerial, mode, dataType);
    }

    /**
     * 获取所有温湿度信息
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryTempHumiInfoList")
    public JSONObject queryTempHumiInfoList(@RequestParam String deviceSerial) {
        return humitureClient.queryTempHumiInfoList(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 获取单个温湿度信息
     *
     * @param deviceSerial
     * @param id           温湿度传感器通道编号
     * @return
     */
    @GetMapping("/queryTempHumiInfo")
    public JSONObject queryTempHumiInfo(@RequestParam String deviceSerial, @RequestParam Integer id) {
        return humitureClient.queryTempHumiInfo(ysService.getTokenFromCache(), deviceSerial, id);
    }
}
