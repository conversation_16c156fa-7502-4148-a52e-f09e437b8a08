package com.byzp.platform.controller.api.ys;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.ys.FallDeviceClient;
import com.byzp.platform.client.ys.YsClient;
import com.byzp.kylin.core.annotation.ApiVersion;
import com.byzp.platform.service.ys.YsServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 跌倒检测雷达远程查询和设置接口
 */
@RestController
@ApiVersion(1)
@RequestMapping("/api/fallDevice")
@CrossOrigin
@Slf4j
public class DeviceFallingController {

    @Resource
    private FallDeviceClient fallDeviceClient;
    @Resource
    private YsClient ysClient;
    @Resource
    private YsServiceImpl ysService;

    /**
     * 查询有人进入检测使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryHumanDetectionEnabled")
    public JSONObject queryHumanDetectionEnabled(@RequestParam String deviceSerial) {
        return fallDeviceClient.queryInDetectEnabled(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 设置有人进入检测使能
     *
     * @param deviceSerial
     * @param enable
     * @return
     */
    @GetMapping("/setHumanDetectionEnabled")
    public JSONObject setHumanDetectionEnabled(@RequestParam String deviceSerial, @RequestParam boolean enable) {
        JSONObject data = new JSONObject();
        data.put("data", enable);
        return fallDeviceClient.setInDetectEnabled(ysService.getTokenFromCache(), deviceSerial, enable);
    }

    /**
     * 查询有人进入告警消息通知使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryHumanDetectionAlarmEnabled")
    public JSONObject queryHumanDetectionAlarmEnabled(@RequestParam String deviceSerial) {
        return fallDeviceClient.queryHumanDetectionAlarmEnabled(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置有人进入告警消息通知使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/setHumanDetectionAlarmEnabled")
    public JSONObject setHumanDetectionAlarmEnabled(@RequestParam String deviceSerial, @RequestParam boolean enabled) {
        return fallDeviceClient.setHumanDetectionAlarmEnabled(ysService.getTokenFromCache(), deviceSerial, enabled);
    }

    /**
     * 查询有人进入告警消息上报使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryHumanDetectionAlarmReportEnabled")
    public JSONObject queryHumanDetectionAlarmReportEnabled(@RequestParam String deviceSerial) {
        return fallDeviceClient.queryHumanDetectionAlarmReportEnabled(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 设置有人进入告警消息上报使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/setHumanDetectionAlarmReportEnabled")
    public JSONObject setHumanDetectionAlarmReportEnabled(@RequestParam String deviceSerial, @RequestParam boolean enabled) {
        return fallDeviceClient.setHumanDetectionAlarmReportEnabled(ysService.getTokenFromCache(), deviceSerial, enabled);
    }


    /**
     * 查询跌倒检测规则
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryFallingDownDetectionRule")
    public JSONObject queryFallingDownDetectionRule(@RequestParam String deviceSerial) {
        return fallDeviceClient.queryFallingDownDetectionRule(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 设置跌倒检测规则
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/setFallingDownDetectionRule")
    public JSONObject setFallingDownDetectionRule(@RequestParam String deviceSerial, @RequestParam Integer leftDistance, @RequestParam Integer frontDistance, @RequestParam Integer rightDistance) {
        return fallDeviceClient.setFallingDownDetectionRule(ysService.getTokenFromCache(), deviceSerial, leftDistance, frontDistance, rightDistance);
    }

    /**
     * 查询有人跌倒消息通知使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryPeopleFallingDownNoticeEnabled")
    public JSONObject queryPeopleFallingDownNoticeEnabled(@RequestParam String deviceSerial) {
        return fallDeviceClient.queryPeopleFallingDownNoticeEnabled(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 设置有人跌倒消息通知使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/setPeopleFallingDownNoticeEnabled")
    public JSONObject setPeopleFallingDownNoticeEnabled(@RequestParam String deviceSerial, @RequestParam boolean enable) {
        return fallDeviceClient.setPeopleFallingDownNoticeEnabled(ysService.getTokenFromCache(), deviceSerial, enable);
    }

    /**
     * 查询最后一条信息
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryRecentNotice")
    public JSONObject queryRecentNotice(@RequestParam String deviceSerial) {
        return fallDeviceClient.queryRecentNotice(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 设置最后一条信息
     *
     * @param deviceSerial
     * @param data         0-暂无数据，1-有人出现，2-有人离开，3-有人跌倒
     * @return
     */
    @GetMapping("/setRecentNotice")
    public JSONObject setRecentNotice(@RequestParam String deviceSerial, @RequestParam Integer data) {
        return fallDeviceClient.setRecentNotice(ysService.getTokenFromCache(), deviceSerial, data);
    }


    /**
     * 查询网络信息
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/netStatus")
    public JSONObject netStatus(@RequestParam String deviceSerial) {
        return fallDeviceClient.netStatus(ysService.getTokenFromCache(), deviceSerial);
    }


    /**
     * 查询有人跌倒告警上报使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryPeopleFallingDownReportEnabled")
    public JSONObject queryPeopleFallingDownReportEnabled(@RequestParam String deviceSerial) {
        return fallDeviceClient.queryPeopleFallingDownReportEnabled(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 设置有人跌倒告警上报使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/setPeopleFallingDownReportEnabled")
    public JSONObject setPeopleFallingDownReportEnabled(@RequestParam String deviceSerial, @RequestParam boolean enable) {
        return fallDeviceClient.setPeopleFallingDownReportEnabled(ysService.getTokenFromCache(), deviceSerial, enable);
    }

    /**
     * 查询滞留时长
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryStayTime")
    public JSONObject queryStayTime(@RequestParam String deviceSerial) {
        return fallDeviceClient.queryStayTime(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 设置滞留时长
     *
     * @param deviceSerial
     * @param minutes      30,60,120,360,720
     * @return
     */
    @GetMapping("/setStayTime")
    public JSONObject setStayTime(@RequestParam String deviceSerial, @RequestParam Integer minutes) {
        return fallDeviceClient.setStayTime(ysService.getTokenFromCache(), deviceSerial, minutes);
    }

    /**
     * 查询滞留检测使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/queryStayEnable")
    public JSONObject queryStayEnable(@RequestParam String deviceSerial) {
        return fallDeviceClient.queryStayEnable(ysService.getTokenFromCache(), deviceSerial);
    }

    /**
     * 设置滞留检测使能
     *
     * @param deviceSerial
     * @return
     */
    @GetMapping("/setStayEnable")
    public JSONObject setStayEnable(@RequestParam String deviceSerial, @RequestParam boolean enable) {
        return fallDeviceClient.setStayEnable(ysService.getTokenFromCache(), deviceSerial, enable);
    }

}
