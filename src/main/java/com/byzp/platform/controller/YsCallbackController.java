package com.byzp.platform.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.byzp.kylin.core.annotation.Original;
import com.byzp.platform.client.CallbackClient;
import com.byzp.platform.mapper.po.CloudApplication;
import com.byzp.platform.service.CloudApplicationServiceImpl;
import com.byzp.platform.utils.WebhookMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 萤石回调
 */
@RequestMapping(value = "/ysCallback")
@RestController
@CrossOrigin
@Slf4j
public class YsCallbackController {
    @Resource
    private CallbackClient callbackClient;
    @Resource
    private CloudApplicationServiceImpl cloudApplicationService;

    /**
     * 接受告警信息推送接口
     */
    @Original
    @RequestMapping("/callback")
    public ResponseEntity<String> callback(@RequestHeader HttpHeaders header, @RequestBody String body) {
        final List<String> t = header.get("t");
        WebhookMessage receiveMessage = null;
        log.info("消息获取时间:{}, 请求头:{},请求体:{}", System.currentTimeMillis(), JSON.toJSONString(header), body);
        try {
            receiveMessage = JSON.parseObject(body, WebhookMessage.class);
            //处理消息
//            alarmService.doLog(receiveMessage,body);
            String deviceSerial = getDeviceSerial(receiveMessage);
            CloudApplication application = cloudApplicationService.getApplicationByDeviceSerial(deviceSerial);
            if (application != null){
                String ys = callbackClient.callback(application.getNoticeCallbackUrl(), body, "ys");
                log.info("萤石回调结果：" + ys);
            }
        } catch (Exception e) {
            log.error("机器人上报信息处理失败{}", e.getMessage(), e);
        }
        //必须进行返回
        Map<String, String> result = new HashMap<>(1);
        assert receiveMessage != null;
        String messageId = receiveMessage.getHeader().getMessageId();
        result.put("messageId", messageId);
        final ResponseEntity<String> resp = ResponseEntity.ok(JSON.toJSONString(result));
        log.info("返回的信息:{}", JSON.toJSONString(result));
        return resp;
    }

//    /**
//     * 接受告警信息推送接口
//     */
//    @Original
//    @RequestMapping("/callback1")
//    public ResponseEntity<String> callback1(@RequestHeader HttpHeaders header, @RequestBody String body) {
//        final List<String> t = header.get("t");
//        WebhookMessage receiveMessage = null;
//        log.info("消息获取时间:{}, 请求头:{},请求体:{}", System.currentTimeMillis(), JSON.toJSONString(header), body);
//        try {
//            receiveMessage = JSON.parseObject(body, WebhookMessage.class);
//            //处理消息
////            alarmService.doLog(receiveMessage,body);
//            String deviceSerial = getDeviceSerial(receiveMessage);
//            callbackClient.callback("http://192.168.4.3:8001/ysCallback/callback", body, "ys");
//        } catch (Exception e) {
//            log.error("机器人上报信息处理失败{}", e.getMessage(), e);
//        }
//        //必须进行返回
//        Map<String, String> result = new HashMap<>(1);
//        assert receiveMessage != null;
//        String messageId = receiveMessage.getHeader().getMessageId();
//        result.put("messageId", messageId);
//        final ResponseEntity<String> resp = ResponseEntity.ok(JSON.toJSONString(result));
//        log.info("返回的信息:{}", JSON.toJSONString(result));
//        return resp;
//    }

    public String getDeviceSerial(WebhookMessage receiveMessage){
        JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(receiveMessage.getBody()), JSONObject.class);
        JSONObject jsonObjectHeader = JSON.parseObject(JSON.toJSONString(receiveMessage.getHeader()), JSONObject.class);
        String type = jsonObjectHeader.getString("type");
        String warnCode=null;
        String deviceId=null;

        if("ys.iot".equals(type)){
            log.info("系统接收到ys.iot 国标告警消息 开始进行记录日志并推送逻辑");

            //消息编码
            warnCode = jsonObject.getString("identifier");
            //设备号
            deviceId = jsonObject.getString("deviceId");

        }else if("ys.alarm".equals(type)){
            log.info("系统接收到ys.alarm 告警消息 开始进行记录日志并推送逻辑");

            //消息编码
            warnCode = jsonObject.getString("alarmType");
            //设备号
            deviceId = jsonObject.getString("devSerial");

        }else if("ys.onoffline".equals(type)){
            log.info("系统接收到ys.onoffline 上下线消息 开始进行设备上下线逻辑");

            //消息编码
            warnCode = jsonObject.getString("msgType");

            //设备号
            deviceId = jsonObject.getString("subSerial");

        }else{
            log.error("无法识别的ys消息类型");
        }
        return deviceId;
    }
}
