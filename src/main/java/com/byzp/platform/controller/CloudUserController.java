package com.byzp.platform.controller;

import com.byzp.platform.dto.CloudUserDTO;
import com.byzp.platform.param.CloudUserParam;
import com.byzp.platform.service.CloudUserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 云平台用户
 */
@RequestMapping(value = "/cloudUser")
@RestController
@CrossOrigin
@Slf4j
public class CloudUserController {

    @Resource
    private CloudUserServiceImpl cloudUserService;

    /**
     * 注册云平台账号
     * @param param
     */
    @PostMapping("/register")
    public CloudUserDTO register(@RequestBody CloudUserParam param){
        return cloudUserService.registerAndLogin(param);
    }

    /**
     * 密码登录
     * @param param
     */
    @PostMapping("loginByPassword")
    public CloudUserDTO loginByPassword(@RequestBody CloudUserParam param){
        return cloudUserService.loginByPassword(param);
    }

    /**
     * 验证码登录
     * @param param
     */
    @PostMapping("/loginByValidCode")
    public CloudUserDTO loginByValidCode(@RequestBody CloudUserParam param){
        return cloudUserService.loginByValidCode(param);
    }
}
