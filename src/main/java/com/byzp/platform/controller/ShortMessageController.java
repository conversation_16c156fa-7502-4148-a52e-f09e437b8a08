package com.byzp.platform.controller;

import com.anji.captcha.model.common.ResponseModel;
import com.anji.captcha.model.vo.CaptchaVO;
import com.anji.captcha.service.CaptchaService;
import com.anji.captcha.util.StringUtils;
import com.byzp.kylin.core.abnormal.KylinBusinessException;
import com.byzp.platform.service.ShortMessageSendRecordServiceImpl;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 滑块发送短信
 */
@RequestMapping(value = "/shortMessage")
@RestController
@CrossOrigin
@Slf4j
public class ShortMessageController {

    @Resource
    private CaptchaService captchaService;
    @Resource
    private ShortMessageSendRecordServiceImpl shortMessageSendRecordService;

    /**
     * 获取滑块信息
     * @param data
     * @param request
     * @return
     */
    @RequestMapping(value = "/get", method = RequestMethod.POST)
    public ResponseModel get(@RequestBody CaptchaVO data, HttpServletRequest request) {
        assert request.getRemoteHost() != null;

        data.setBrowserInfo(getRemoteId(request));
        return this.captchaService.get(data);
    }

    /**
     * 校验滑块位置
     * @param data
     * @param request
     * @return
     */
    @RequestMapping(value = "/check", method = RequestMethod.POST)
    public ResponseModel check(@RequestBody CaptchaVO data, HttpServletRequest request) {
        data.setBrowserInfo(getRemoteId(request));
        return this.captchaService.check(data);
    }

    /**
     * 发送验证码短信
     * @param mobileNo 手机号
     * @param smsSecretKey 滑块校验密钥
     */
    @RequestMapping(value = "/sendValidateCode", method = RequestMethod.GET)
    public void sendValidateCode(@RequestParam("mobileNo") String mobileNo,
                                      @RequestParam("smsSecretKey") String smsSecretKey) {;
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaVerification(smsSecretKey);
        ResponseModel verification = captchaService.verification(captchaVO);
        if (!verification.isSuccess()){
            throw new KylinBusinessException(500, "验证失败");
        }
        shortMessageSendRecordService.sendValidateCodeMessage(mobileNo);
    }

//    /**
//     * 校验验证码
//     * @param mobileNo
//     * @param validateCode
//     */
//    @GetMapping("/validateCode")
//    public void validateCode(@RequestParam String mobileNo,
//                             @RequestParam String validateCode){
//
//    }

    public static final String getRemoteId(HttpServletRequest request) {
        String xfwd = request.getHeader("X-Forwarded-For");
        String ip = getRemoteIpFromXfwd(xfwd);
        String ua = request.getHeader("user-agent");
        return StringUtils.isNotBlank(ip) ? ip + ua : request.getRemoteAddr() + ua;
    }

    private static String getRemoteIpFromXfwd(String xfwd) {
        if (StringUtils.isNotBlank(xfwd)) {
            String[] ipList = xfwd.split(",");
            return StringUtils.trim(ipList[0]);
        } else {
            return null;
        }
    }
}
