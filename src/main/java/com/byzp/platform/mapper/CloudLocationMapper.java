package com.byzp.platform.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudLocationDTO;
import com.byzp.kylin.data.api.KylinMapper;
import com.byzp.platform.mapper.po.CloudLocation;
import com.byzp.platform.param.CloudLocationParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface CloudLocationMapper extends KylinMapper<CloudLocation> {

    List<CloudLocationDTO> queryList(@Param("param")CloudLocationParam param);

    Page<CloudLocationDTO> queryPage(Page<CloudLocation> page, CloudLocationParam param);
}
