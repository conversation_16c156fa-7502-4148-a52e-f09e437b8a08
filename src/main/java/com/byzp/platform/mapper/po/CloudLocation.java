package com.byzp.platform.mapper.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudLocation implements Serializable {
    @Serial
    private static final long serialVersionUID = -3216291797968885384L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 名称
     */
    private String name;
    /**
     * 备注
     */
    private String remark;
    /**
     * 用户id
     */
    private Long userId;

    private Date createTime;

}
