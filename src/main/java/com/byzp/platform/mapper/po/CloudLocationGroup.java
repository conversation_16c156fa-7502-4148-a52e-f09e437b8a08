package com.byzp.platform.mapper.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudLocationGroup implements Serializable {
    @Serial
    private static final long serialVersionUID = -3239758072723622513L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 位置id
     */
    private Long locationId;
    /**
     * 分组名称
     */
    private String name;
    /**
     * 备注
     */
    private String remark;

    private Date createTime;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
}
