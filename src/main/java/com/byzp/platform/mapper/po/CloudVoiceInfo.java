package com.byzp.platform.mapper.po;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudVoiceInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1247511763639275443L;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 声纹id（同一个应用下唯一）
     */
    private String voiceId;
    /**
     * 声纹名称
     */
    private String voiceName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 声纹base64
     */
    private String voiceBase64;

    private Date createTime;
}
