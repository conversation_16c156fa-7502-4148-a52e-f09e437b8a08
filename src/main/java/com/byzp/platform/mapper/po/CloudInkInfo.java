package com.byzp.platform.mapper.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudInkInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 2380931930337389557L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 数据库表字段: ink_name, 墨水屏名称
     */
    private String inkName;
    /**
     * 数据库表字段: cloud_application_id, 应用id
     */
    private Long cloudApplicationId;
    /**
     * 墨水屏配置信息(下发给硬件的模版)
     */
    private String settingInfo;
    /**
     * 墨水屏配置信息（供前端回显）
     */
    private String sourceSettingInfo;
    /**
     * 数据库表字段: create_time
     */
    private Date createTime;
}
