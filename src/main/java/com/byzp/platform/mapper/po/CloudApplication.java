package com.byzp.platform.mapper.po;

import cn.hutool.db.DaoTemplate;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class CloudApplication implements Serializable {
    @Serial
    private static final long serialVersionUID = 1719189690149013253L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 云平台账号id
     */
    private Long cloudUserId;
    /**
     * 应用名称
     */
    private String appName;
    /**
     * 应用备注信息
     */
    private String remark;
    /**
     * 第三方通知回调接口
     */
    private String noticeCallbackUrl;
    /**
     * 应用密钥
     */
    private String appSecret;
    /**
     * 应用id
     */
    private String appId;
    /**
     * 声纹相似度，0-1，数值越大相似度越高，默认0.6
     */
    private BigDecimal voiceSimilarity;

    private Date createTime;

    private Date updateTime;
}
