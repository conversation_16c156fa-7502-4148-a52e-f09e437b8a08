package com.byzp.platform.mapper.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudUser implements Serializable {
    @Serial
    private static final long serialVersionUID = -7830830848756521169L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 账号（手机号）
     */
    private String account;
    /**
     * 密码
     */
    private String password;
    /**
     * 是否删除：0-否，1-是
     */
    private Integer isDelete;
    private Date createTime;

    private Date updateTime;
}
