package com.byzp.platform.mapper.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class DeviceVariety implements Serializable {

    @Serial
    private static final long serialVersionUID = -2880601187341065177L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 型号
     */
    private String model;
    /**
     * 图片链接
     */
    private String picUrl;

    private Integer linkType;

    private Integer type;

    private Integer isDelete;
    /**
     * 是否需要开启音频功能：0-否，1-是
     */
    private Integer needAudio;

    private Date createTime;
}
