package com.byzp.platform.mapper.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class SleepReport implements Serializable {
    @Serial
    private static final long serialVersionUID = 3635699279799458534L;
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 序列号
     */
    private String deviceSerial;
    /**
     * 报告日期
     */
    private String reportDate;
    /**
     * 睡眠报告信息
     */
    private String report;
    /**
     * 创建时间
     */
    private Date createTime;
}
