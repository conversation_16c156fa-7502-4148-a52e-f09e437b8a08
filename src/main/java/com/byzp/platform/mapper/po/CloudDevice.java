package com.byzp.platform.mapper.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudDevice implements Serializable {
    @Serial
    private static final long serialVersionUID = 1003428287063340657L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 设备序列号
     */
    private String deviceSerial;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 型号
     */
    private String model;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 分组id
     */
    private Long cloudLocationGroupId;
    /**
     * 位置id
     */
    private Long cloudLocationId;

    private Date createTime;
    /**
     * 墨水屏id
     */
    private Long cloudInkId;
}
