package com.byzp.platform.mapper.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudDeviceVoiceBind implements Serializable {
    @Serial
    private static final long serialVersionUID = 3982620284138147845L;
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 设备序列号
     */
    private String deviceSerial;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 声纹id
     */
    private String voiceId;

    private Date createTime;

}
