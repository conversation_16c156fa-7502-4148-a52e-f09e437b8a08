package com.byzp.platform.mapper.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
@TableName(value = "sms_send_record")
public class ShortMessageSendRecord implements Serializable {
    @Serial
    private static final long serialVersionUID = -427597682379537679L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 手机号
     */
    @TableField("mobile")
    private String mobile;

    /**
     * 短信类型
     */
    @TableField("sms_type")
    private Integer smsType;

    /**
     * 短信参数
     */
    @TableField("send_param")
    private String sendParam;

    /**
     * 发送响应结果
     */
    @TableField("send_response")
    private String sendResponse;

    @TableField("create_time")
    private Date createTime;

    @TableField("update_time")
    private Date updateTime;
}
