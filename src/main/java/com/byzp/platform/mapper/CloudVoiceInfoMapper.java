package com.byzp.platform.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.kylin.data.api.KylinMapper;
import com.byzp.platform.dto.CloudVoiceInfoDTO;
import com.byzp.platform.mapper.po.CloudVoiceInfo;
import com.byzp.platform.param.CloudVoiceInfoParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CloudVoiceInfoMapper extends KylinMapper<CloudVoiceInfo> {

    String queryPage = """
    <script>
        select
            *
        from
            cloud_voice_info
        where
            cloud_application_id = #{param.cloudApplicationId}
            <if test="param.voiceId != null and param.voiceId != ''">
                and voice_id = #{param.voiceId}
            </if>
            <if test="param.remark != null and param.remark != ''">
                and remark like concat ('%',#{param.remark},'%')
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
                and create_time &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
                and create_time &lt;= #{param.createTimeEnd}
            </if>
        order by create_time desc
    </script>
""";
    @Select(queryPage)
    Page<CloudVoiceInfoDTO> queryPage(Page<CloudVoiceInfo> page, CloudVoiceInfoParam param);
}
