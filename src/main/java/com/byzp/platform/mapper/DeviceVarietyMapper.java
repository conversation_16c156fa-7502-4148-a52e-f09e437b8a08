package com.byzp.platform.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.kylin.data.api.KylinMapper;
import com.byzp.platform.dto.DeviceVarietyDTO;
import com.byzp.platform.mapper.po.DeviceVariety;
import com.byzp.platform.param.DeviceVarietyParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface DeviceVarietyMapper extends KylinMapper<DeviceVariety> {

    String queryPage = """
    <script>
        select
            *
        from
            device_variety
        <where>
            is_delete = 0
            <if test="param.name != null and param.name != ''">
                and name like concat ('%',#{param.name},'%')
            </if>
            <if test="param.model != null and param.model != ''">
                and model like concat ('%',#{param.model},'%')
            </if>
        </where>
        order by id desc
    </script>
""";
    @Select(queryPage)
    Page<DeviceVarietyDTO> queryPage(Page<DeviceVariety> page, DeviceVarietyParam param);
}
