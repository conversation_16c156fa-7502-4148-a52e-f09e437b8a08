package com.byzp.platform.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudDeviceDTO;
import com.byzp.kylin.data.api.KylinMapper;
import com.byzp.platform.mapper.po.CloudDevice;
import com.byzp.platform.param.CloudDeviceParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

@Mapper
public interface CloudDeviceMapper extends KylinMapper<CloudDevice> {

    String queryPage = """
    <script>
        select
            a.*,
            b.app_name cloudApplicationName,
            c.name cloudLocationName,
            d.name cloudLocationGroupName
        from
            cloud_device a 
            inner join cloud_application b on a.cloud_application_id = b.id
            left join cloud_location c on a.cloud_location_id = c.id
            left join cloud_location_group d on a.cloud_location_group_id = d.id
        where
            b.cloud_user_id = #{param.cloudUserId}
            <if test="param.cloudLocationId != null and param.cloudLocationId != 0">
                and a.cloud_location_id = #{param.cloudLocationId}
            </if>
            <if test="param.cloudLocationGroupId != null and param.cloudLocationGroupId != 0">
                and a.cloud_location_group_id = #{param.cloudLocationGroupId}
            </if>
            <if test="param.deviceSerial != null and param.deviceSerial != ''">
                and a.device_serial like concat ('%',#{param.deviceSerial},'%')
            </if>
            <if test="param.cloudApplicationId != null">
                and a.cloud_application_id = #{param.cloudApplicationId}
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
                and a.create_time &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
                and a.create_time &lt;= #{param.createTimeEnd}
            </if>
            order by a.id desc
    </script>
""";
    @Select(queryPage)
    Page<CloudDeviceDTO> queryPage(Page<CloudDevice> page, CloudDeviceParam param);

    String queryCountByInkIdList = """
    <script>
        select
            cloud_ink_id cloudInkId,
            count(1) total
        from
            cloud_device
        where
            cloud_ink_id in 
            <foreach collection="cloudInkIdList" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        group by cloud_ink_id
    </script>
""";
    @Select(queryCountByInkIdList)
    List<Map<Long, Integer>> queryCountByInkIdList(@Param("cloudInkIdList") List<Long> cloudInkIdList);

    @Select("select source_setting_info from cloud_ink_info where id = #{id} and cloud_application_id = #{cloudApplicationId}")
    String selectSourceDetail(@Param("id") Long id,@Param("cloudApplicationId") Long cloudApplicationId);

    String queryListForDiaper = """
            <script>
                select
                    device_serial,
                    device_name,
                    model
                from
                    cloud_device
                where
                    model = #{model} and cloud_application_id = #{cloudApplicationId}
                    <if test="deviceSerial != null and deviceSerial != ''">
                        and device_serial like concat ('%',#{deviceSerial},'%')
                    </if>
            </script>
            """;
    @Select(queryListForDiaper)
    List<CloudDeviceDTO> queryListForDiaper(@Param("cloudApplicationId") Long cloudApplicationId,
                                            @Param("deviceSerial") String deviceSerial,
                                            @Param("model") String model);
}
