package com.byzp.platform.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.platform.dto.CloudApplicationDTO;
import com.byzp.kylin.data.api.KylinMapper;
import com.byzp.platform.mapper.po.CloudApplication;
import com.byzp.platform.param.CloudApplicationParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CloudApplicationMapper extends KylinMapper<CloudApplication> {

    String queryPage = """
    <script>
        select
            *
        from
            cloud_application
        where
            cloud_user_id = #{param.cloudUserId}
            <if test="param.appName != null and param.appName != ''">
                and app_name like concat ('%',#{param.appName},'%')
            </if>
    </script>
""";
    @Select(queryPage)
    Page<CloudApplicationDTO> queryPage(Page<CloudApplication> page, CloudApplicationParam param);
}
