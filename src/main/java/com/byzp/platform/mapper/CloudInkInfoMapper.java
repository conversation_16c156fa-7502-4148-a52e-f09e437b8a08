package com.byzp.platform.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.kylin.data.api.KylinMapper;
import com.byzp.platform.dto.CloudInkInfoDTO;
import com.byzp.platform.mapper.po.CloudInkInfo;
import com.byzp.platform.param.CloudInkInfoParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

@Mapper
public interface CloudInkInfoMapper extends KylinMapper<CloudInkInfo> {

    String queryPage = """
    <script>
        select
            id,
            ink_name,
            setting_info,
            source_setting_info,
            create_time
        from
            cloud_ink_info
        <where>
            cloud_application_id = #{param.cloudApplicationId}
            <if test="param.id != null">
                and id = #{param.id}
            </if>
            <if test="param.inkName != null and param.inkName != ''">
                and ink_name like concat ('%',#{param.inkName},'%')
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
                and create_time &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
                and create_time &lt;= #{param.createTimeEnd}
            </if>
        </where>
        order by id desc
    </script>
""";
    @Select(queryPage)
    Page<CloudInkInfoDTO> queryPage(Page<CloudInkInfo> page, CloudInkInfoParam param);

}
