package com.byzp.platform.mapper;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.byzp.kylin.data.api.KylinMapper;
import com.byzp.platform.dto.CloudLocationGroupDTO;
import com.byzp.platform.mapper.po.CloudLocationGroup;
import com.byzp.platform.param.CloudLocationGroupParam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface CloudLocationGroupMapper extends K<PERSON>inMapper<CloudLocationGroup> {

    String queryPage = """
    <script>
        select
            a.*,
            b.name locationName
        from
            cloud_location_group a 
            inner join cloud_location b on a.location_id = b.id
        where
            a.cloud_application_id = #{param.cloudApplicationId}
            <if test="param.name != null and param.name != ''">
              and a.name like concat ('%',#{param.name},'%')
            </if>
            <if test="param.createTimeStart != null and param.createTimeStart != ''">
              and a.create_time &gt;= #{param.createTimeStart}
            </if>
            <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
              and a.create_time &lt;= #{param.createTimeEnd}
            </if>
            <if test="param.locationId != null">
                and a.location_id = #{param.locationId}
            </if>
        order by a.id desc
    </script>
""";
    @Select(queryPage)
    Page<CloudLocationGroupDTO> queryPage(Page<CloudLocationGroup> page, CloudLocationGroupParam param);

    String selectListByLocationId = """
    <script>
        select
            *
        from
            cloud_location_group
        where
            location_id = #{locationId}
    </script>
""";
    @Select(selectListByLocationId)
    List<CloudLocationGroupDTO> selectListByLocationId(@Param("locationId") Long locationId);
}
