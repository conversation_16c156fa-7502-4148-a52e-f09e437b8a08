package com.byzp.platform.callbacks;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.byzp.kylin.util.collection.CollectionUtils;
import com.byzp.platform.client.CallbackClient;
import com.byzp.platform.mapper.CloudApplicationMapper;
import com.byzp.platform.mapper.CloudDeviceMapper;
import com.byzp.platform.mapper.CloudVoiceInfoMapper;
import com.byzp.platform.mapper.po.CloudApplication;
import com.byzp.platform.mapper.po.CloudDevice;
import com.byzp.platform.mapper.po.CloudVoiceInfo;
import com.byzp.platform.mapper.po.SleepReport;
import com.byzp.platform.service.CloudApplicationServiceImpl;
import com.byzp.platform.utils.AudioConverter;
import com.byzp.platform.utils.SpringContextUtil;
import com.byzp.platform.utils.kdxf.KdxfUtil;
import com.byzp.platform.utils.kdxf.ScoreInfo;
import com.byzp.platform.utils.kdxf.SparkMultiIatModel;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.WebSocket;
import org.eclipse.paho.client.mqttv3.*;
import org.springframework.context.ApplicationContext;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class MqttCallbackConsumer implements MqttCallbackExtended {

    private CloudApplicationServiceImpl cloudApplicationService;
    private String mediaPath;
    private MqttClient mqttClient;
    private String topic;
    private int qos;
    private boolean isSubscribed = false;
    private final int maxRetryAttempts;
    private final long initialRetryDelay;
    private final ScheduledExecutorService scheduler;
    private AtomicInteger retryCount = new AtomicInteger(0);

    public MqttCallbackConsumer(CloudApplicationServiceImpl cloudApplicationService, String mediaPath,
                                MqttClient mqttClient, String topic, int qos, int maxRetryAttempts, long initialRetryDelay){
        this.cloudApplicationService = cloudApplicationService;
        this.mediaPath = mediaPath;
        this.mqttClient = mqttClient;
        this.topic = topic;
        this.qos = qos;
        this.maxRetryAttempts = maxRetryAttempts;
        this.initialRetryDelay = initialRetryDelay;
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
    }



    @Override
    public void connectionLost(Throwable throwable) {
        log.info("connectionLost:", throwable);
        isSubscribed = false;
    }

    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("连接完成 - 重连状态: " + reconnect);
        if (reconnect) {
            // 重置重试计数器
            retryCount.set(0);
            // 自动重连后恢复订阅
            log.info("尝试恢复订阅: " + topic);
            resubscribe();
        } else {
            log.info("首次连接成功");
        }
    }

    // 恢复订阅方法（带重试逻辑）
    private void resubscribe() {
        // 使用单独线程执行订阅
        new Thread(() -> {
            try {
                // 等待连接稳定
                Thread.sleep(1000);

                if (mqttClient != null && mqttClient.isConnected() && !isSubscribed) {
                    attemptSubscribe(0);
                }
            } catch (Exception e) {
                log.info("订阅初始化失败: " + e.getMessage());
                attemptSubscribe(1); // 初始尝试
            }
        }).start();
    }

    // 带指数退避的订阅重试方法
    private void attemptSubscribe(int attempt) {
        if (attempt >= maxRetryAttempts) {
            log.info("达到最大重试次数，订阅失败: " + topic);
            return;
        }

        int currentAttempt = retryCount.incrementAndGet();
        long delay = initialRetryDelay * (long) Math.pow(2, currentAttempt - 1);

        log.info("尝试订阅 (第" + currentAttempt + "次): " + topic +
                "，延迟: " + delay + "ms");

        scheduler.schedule(() -> {
            try {
                if (mqttClient.isConnected()) {
                    mqttClient.subscribe(topic, qos);
                    isSubscribed = true;
                    retryCount.set(0);
                    log.info("订阅成功: " + topic);
                } else {
                    log.info("订阅失败: 客户端未连接");
                    attemptSubscribe(currentAttempt); // 重试
                }
            } catch (MqttException e) {
                log.info("订阅失败 (第" + currentAttempt + "次): " +
                        e.getMessage() + "，将重试...");
                attemptSubscribe(currentAttempt); // 重试
            }
        }, delay, TimeUnit.MILLISECONDS);
    }

    // 关闭资源
    public void shutdown() {
        scheduler.shutdown();
    }

    @Override
    public void messageArrived(String topic, MqttMessage mqttMessage) throws Exception {
        try {
            String payload = new String(mqttMessage.getPayload());
            log.info("receiveMessage: topic=" + topic + ",Qos=" + mqttMessage.getQos() + ",payload=" + new String(mqttMessage.getPayload()));
            JSONObject payloadObject = JSONObject.parseObject(payload);
            if ("sleep_monitor".equals(payloadObject.getString("msgType"))) {
                CloudApplication cloudApplication = cloudApplicationService.getApplicationByDeviceSerial(payloadObject.getString("clientId"));
                if (cloudApplication != null) {
                    JSONObject param = new JSONObject();
                    param.put("msgId", UUID.randomUUID().toString().replaceAll("-", ""));
                    param.put("msgType", "sleep_monitor");
                    param.put("msgTime", Long.valueOf(payloadObject.getString("time")));
                    param.put("deviceSerial", payloadObject.getString("clientId"));
                    JSONObject data = new JSONObject();
                    data.put("msg", payloadObject.getJSONArray("payload"));
                    param.put("data", data);
                    CallbackClient callbackClient = (CallbackClient) SpringContextUtil.getBean("callbackClient");
                    String callback = callbackClient.callback(cloudApplication.getNoticeCallbackUrl(), param.toJSONString(), "byzp");
                    log.info("睡眠报告回调结果:" + callback);
                }
            } else if ("audio_transfer".equals(payloadObject.getString("msgType"))) {
                // 硬件录入的录音
                String pcmBase64 = payloadObject.getString("payload");
                CloudApplication cloudApplication = cloudApplicationService.getApplicationByDeviceSerial(payloadObject.getString("clientId"));
                if (cloudApplication != null) {
                    // 用于获取声纹信息
                    String mp3Path = mediaPath + UUID.randomUUID().toString().replaceAll("-", "") + ".mp3";
                    AudioConverter.convertPcmBase64ToMp3File(pcmBase64, mp3Path);
                    String mp3Base64 = AudioConverter.audioFileToBase64(mp3Path);
                    List<ScoreInfo> scoreInfos = KdxfUtil.searchFeatures(payloadObject.getString("clientId"), mp3Base64);
                    if (CollectionUtils.isNotEmpty(scoreInfos)) {
                        ScoreInfo scoreInfo = scoreInfos.stream().max(Comparator.comparing(ScoreInfo::getScore)).get();
                        // 一般相似度在0.6-1之间，表示匹配
//                        if (scoreInfo.getScore().compareTo(cloudApplication.getVoiceSimilarity()) > 0) {
                            // 说明声纹匹配，将声纹转成pcm格式的byte[]，进行转文字处理，再将文字和声纹信息推送给应用配置的回调地址
                            byte[] pcmBytes = AudioConverter.decodeBase64ToByteArray(pcmBase64);
                            translateAudioToText(pcmBytes, payloadObject.getString("clientId"), scoreInfo.getFeatureId(),
                                    cloudApplication.getNoticeCallbackUrl(), UUID.randomUUID().toString().replaceAll("-", ""),
                                    mp3Base64, payloadObject.getString("msgType"), Long.valueOf(payloadObject.getString("time")), null,scoreInfo.getScore(), payloadObject.getString("rand"));
//                        } else {
//                            log.info("声纹不匹配：scoreInfo=" + JSONObject.toJSONString(scoreInfo) + ", 应用信息=" + JSONObject.toJSONString(cloudApplication));
//                        }
                    }
                }
//
            } else if ("alarm_sos".equals(payloadObject.getString("msgType"))) {
                CloudApplication cloudApplication = cloudApplicationService.getApplicationByDeviceSerial(payloadObject.getString("clientId"));
                if (cloudApplication != null) {
                    JSONObject param = new JSONObject();
                    param.put("msgId", UUID.randomUUID().toString().replaceAll("-", ""));
                    param.put("msgType", "alarm_sos");
                    param.put("msgTime", Long.valueOf(payloadObject.getString("time")));
                    param.put("deviceSerial", payloadObject.getString("clientId"));
                    JSONObject data = new JSONObject();
                    data.put("msg", "");
                    param.put("data", data);
                    CallbackClient callbackClient = (CallbackClient) SpringContextUtil.getBean("callbackClient");
                    String callback = callbackClient.callback(cloudApplication.getNoticeCallbackUrl(), param.toJSONString(), "byzp");
                    log.info("拉伸报警回调结果:" + callback);
                }
            } else if ("client.connected".equals(payloadObject.getString("msgType"))){
                CloudApplication cloudApplication = cloudApplicationService.getApplicationByDeviceSerial(payloadObject.getString("clientId"));
                if (cloudApplication != null) {
                    JSONObject param = new JSONObject();
                    param.put("msgId", UUID.randomUUID().toString().replaceAll("-", ""));
                    param.put("msgType", "client.connected");
                    param.put("msgTime", Long.valueOf(payloadObject.getString("time")));
                    param.put("deviceSerial", payloadObject.getString("clientId"));
                    JSONObject data = new JSONObject();
                    param.put("data", data);
                    CallbackClient callbackClient = (CallbackClient) SpringContextUtil.getBean("callbackClient");
                    String callback = callbackClient.callback(cloudApplication.getNoticeCallbackUrl(), param.toJSONString(), "byzp");
                    log.info("设备在线:" + callback);
                }
            } else if ("client.disconnected".equals(payloadObject.getString("msgType"))){
                CloudApplication cloudApplication = cloudApplicationService.getApplicationByDeviceSerial(payloadObject.getString("clientId"));
                if (cloudApplication != null) {
                    JSONObject param = new JSONObject();
                    param.put("msgId", UUID.randomUUID().toString().replaceAll("-", ""));
                    param.put("msgType", "client.disConnected");
                    param.put("msgTime", Long.valueOf(payloadObject.getString("time")));
                    param.put("deviceSerial", payloadObject.getString("clientId"));
                    JSONObject data = new JSONObject();
                    param.put("data", data);
                    CallbackClient callbackClient = (CallbackClient) SpringContextUtil.getBean("callbackClient");
                    String callback = callbackClient.callback(cloudApplication.getNoticeCallbackUrl(), param.toJSONString(), "byzp");
                    log.info("设备离线:" + callback);
                }
            } else if ("SLEEP_value".equals(payloadObject.getString("msgType"))){
                String deviceSerial = payloadObject.getString("clientId");
                JSONObject detail = payloadObject.getJSONObject("payload");
                Long startTime = detail.getLong("startTime");
                String reportDate = DateUtil.format(new Date(startTime *1000), "yyyy-MM-dd");
                SleepReport sleepReport = new SleepReport();
                sleepReport.setReportDate(reportDate);
                sleepReport.setDeviceSerial(deviceSerial);
                sleepReport.setReport(detail.toJSONString());
                sleepReport.setCreateTime(new Date());
                cloudApplicationService.saveSleepReport(sleepReport);
            } else if ("lc_warn".equals(payloadObject.getString("msgType"))) {
                CloudApplication cloudApplication = cloudApplicationService.getApplicationByDeviceSerial(payloadObject.getString("clientId"));
                if (cloudApplication != null) {
                    JSONObject param = new JSONObject();
                    param.put("msgId", UUID.randomUUID().toString().replaceAll("-", ""));
                    param.put("msgType", "lc_warn");
                    param.put("msgTime", System.currentTimeMillis()/1000);
                    param.put("deviceSerial", payloadObject.getString("clientId"));
                    JSONObject data = new JSONObject();
                    data.put("msg", payloadObject.getJSONArray("payload"));
                    param.put("data", data);
                    CallbackClient callbackClient = (CallbackClient) SpringContextUtil.getBean("callbackClient");
                    String callback = callbackClient.callback(cloudApplication.getNoticeCallbackUrl(), param.toJSONString(), "byzp");
                    log.info("离床告警回调结果:" + callback);
                }
            } else if ("xl_warn".equals(payloadObject.getString("msgType"))) {
                CloudApplication cloudApplication = cloudApplicationService.getApplicationByDeviceSerial(payloadObject.getString("clientId"));
                if (cloudApplication != null) {
                    JSONObject param = new JSONObject();
                    param.put("msgId", UUID.randomUUID().toString().replaceAll("-", ""));
                    param.put("msgType", "xl_warn");
                    param.put("msgTime", System.currentTimeMillis()/1000);
                    param.put("deviceSerial", payloadObject.getString("clientId"));
                    JSONObject data = new JSONObject();
                    data.put("msg", payloadObject.getJSONArray("payload"));
                    param.put("data", data);
                    CallbackClient callbackClient = (CallbackClient) SpringContextUtil.getBean("callbackClient");
                    String callback = callbackClient.callback(cloudApplication.getNoticeCallbackUrl(), param.toJSONString(), "byzp");
                    log.info("心率告警回调结果:" + callback);
                }
            } else if ("hx_warn".equals(payloadObject.getString("msgType"))) {
                CloudApplication cloudApplication = cloudApplicationService.getApplicationByDeviceSerial(payloadObject.getString("clientId"));
                if (cloudApplication != null) {
                    JSONObject param = new JSONObject();
                    param.put("msgId", UUID.randomUUID().toString().replaceAll("-", ""));
                    param.put("msgType", "hx_warn");
                    param.put("msgTime", System.currentTimeMillis()/1000);
                    param.put("deviceSerial", payloadObject.getString("clientId"));
                    JSONObject data = new JSONObject();
                    data.put("msg", payloadObject.getJSONArray("payload"));
                    param.put("data", data);
                    CallbackClient callbackClient = (CallbackClient) SpringContextUtil.getBean("callbackClient");
                    String callback = callbackClient.callback(cloudApplication.getNoticeCallbackUrl(), param.toJSONString(), "byzp");
                    log.info("呼吸告警回调结果:" + callback);
                }
            } else if ("Recording_input".equals(payloadObject.getString("msgType"))) {
                log.info("收到硬件录入的录音");
                String pcmBase64 = payloadObject.getString("payload");
                // 用于获取声纹信息
                String mp3Path = mediaPath + UUID.randomUUID().toString().replaceAll("-", "") + ".mp3";
                AudioConverter.convertPcmBase64ToMp3File(pcmBase64, mp3Path);
                String mp3Base64 = AudioConverter.audioFileToBase64(mp3Path);
                String voiceId = payloadObject.getString("identification_ID");
                Long cloudApplicationId = payloadObject.getLong("cloudApplicationId");
                CloudApplication cloudApplication = cloudApplicationService.queryDetail(cloudApplicationId);
                if (cloudApplication == null){
                    log.info("应用信息不存在，cloudApplicationId=" + cloudApplicationId);
                    return;
                }
                CloudVoiceInfoMapper cloudVoiceInfoMapper = (CloudVoiceInfoMapper) SpringContextUtil.getBean("cloudVoiceInfoMapper");
                CloudVoiceInfo cloudVoiceInfo = cloudVoiceInfoMapper.selectOne(new QueryWrapper<CloudVoiceInfo>()
                        .lambda().eq(CloudVoiceInfo::getVoiceId, voiceId)
                        .eq(CloudVoiceInfo::getCloudApplicationId, cloudApplicationId));
                if (cloudVoiceInfo != null) {
                    cloudVoiceInfo.setVoiceBase64(mp3Base64);
                    cloudVoiceInfoMapper.updateById(cloudVoiceInfo);
                }else {
                    cloudVoiceInfo = new CloudVoiceInfo();
                    cloudVoiceInfo.setVoiceId(voiceId);
                    cloudVoiceInfo.setVoiceBase64(mp3Base64);
                    cloudVoiceInfo.setCloudApplicationId(cloudApplicationId);
                    cloudVoiceInfo.setCreateTime(new Date());
                    cloudVoiceInfoMapper.insert(cloudVoiceInfo);
                }
                JSONObject param = new JSONObject();
                param.put("msgId", UUID.randomUUID().toString().replaceAll("-", ""));
                param.put("msgType", "voice_record");
                param.put("msgTime", Long.valueOf(payloadObject.getString("time")));
                param.put("deviceSerial", payloadObject.getString("clientId"));
                JSONObject data = new JSONObject();
                data.put("voiceId",voiceId);
                param.put("data", data);
                CallbackClient callbackClient = (CallbackClient) SpringContextUtil.getBean("callbackClient");
                String callback = callbackClient.callback(cloudApplication.getNoticeCallbackUrl(), param.toJSONString(), "byzp");
                log.info("声纹录制推送结果:" + callback);
            } else {
                log.info("未识别的消息类型：" + payloadObject.getString("msgType"));
            }
        }catch (Exception e){
            log.info("error", e);

        }
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        try {
            log.info("deliveryCompleted");
        } catch (Exception e) {
            log.info("deliveryComplete error", e);
        }
    }

    public byte[] decodeBase64ToByteArray(String base64Encoded){
        Base64.Decoder decoder = Base64.getDecoder();
        return decoder.decode(base64Encoded);
    }

    public void translateAudioToText(byte[] audioInfo, String deviceSerial, String voiceId, String callbackUrl,
                                     String msgId,String mp3Base64, String msgType, Long msgTime, String pcmPath,
                                     BigDecimal voiceSimilarity, String randNum) throws Exception{
        // 构建鉴权url
        String authUrl = KdxfUtil.getAuthUrl();
        OkHttpClient client = new OkHttpClient.Builder().build();
        //将url中的 schema http://和https://分别替换为ws:// 和 wss://
        String url = authUrl.toString().replace("http://", "ws://").replace("https://", "wss://");
        Request request = new Request.Builder().url(url).build();
        WebSocket webSocket = client.newWebSocket(request, new SparkMultiIatModel(audioInfo,deviceSerial,voiceId,callbackUrl, msgId, mp3Base64, msgType, msgTime, pcmPath, voiceSimilarity, randNum));
    }
}
