package com.byzp.platform.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CloudLocationDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 5460862229341118044L;

    private Long id;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 名称
     */
    private String name;
    /**
     * 备注
     */
    private String remark;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 分组列表
     */
    private List<CloudLocationGroupDTO> groupList;
    /**
     * 分组数量
     */
    private Integer groupNum;
    /**
     * 设备数量
     */
    private Integer deviceNum;

    private Date createTime;
}
