package com.byzp.platform.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudDeviceDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -1487184699113592731L;

    private Long id;
    /**
     * 设备序列号
     */
    private String deviceSerial;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 型号
     */
    private String model;
    /**
     * 应用id
     */
    private Long cloudApplicationId;
    /**
     * 应用名称
     */
    private String cloudApplicationName;
    /**
     * 分组id
     */
    private Long cloudLocationGroupId;
    /**
     * 分组名称
     */
    private String cloudLocationGroupName;
    /**
     * 位置id
     */
    private Long cloudLocationId;
    /**
     * 位置名称
     */
    private String cloudLocationName;

    private Date createTime;
    /**
     * 图片地址
     */
    private String picUrl;

}
