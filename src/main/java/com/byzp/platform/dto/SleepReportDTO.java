package com.byzp.platform.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class SleepReportDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 5505898418826585210L;

    private Long id;
    /**
     * 序列号
     */
    private String deviceSerial;
    /**
     * 报告日期
     */
    private String reportDate;
    /**
     * 睡眠报告信息
     */
    private String report;
    /**
     * 创建时间
     */
    private Date createTime;
}
