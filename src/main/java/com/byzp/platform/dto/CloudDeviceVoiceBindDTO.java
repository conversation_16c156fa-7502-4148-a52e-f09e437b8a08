package com.byzp.platform.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudDeviceVoiceBindDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -2141143389834735395L;

    private Long id;
    /**
     * 设备序列号
     */
    private String deviceSerial;
    /**
     * 音频id
     */
    private String voiceId;
    /**
     * 音频base64
     */
    private String voiceBase64;
    /**
     * 创建时间
     */
    private Date createTime;
}
