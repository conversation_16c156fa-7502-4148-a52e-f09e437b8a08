package com.byzp.platform.dto.ys;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class ChildDeviceParam implements Serializable {
    @Serial
    private static final long serialVersionUID = -8038363727751289262L;
    /**
     * 子设备序列号，萤石子设备一般为设备或外包装上的9位序列号
     */
    private String childDeviceSerial;
    /**
     * 子设备型号
     */
    private String childDeviceType;
    /**
     * 子设备类目
     */
    private String childDeviceCategory;
    /**
     * 验证码
     */
    private String verificationCode;
}
