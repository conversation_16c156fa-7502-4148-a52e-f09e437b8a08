package com.byzp.platform.dto.ys;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class DeviceInfoParam implements Serializable {
    @Serial
    private static final long serialVersionUID = -3746758550412247511L;

    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 设备序列号
     */
    private String deviceSerial;
    /**
     * 设备名称
     */
    private String deviceName;
    /**
     * 设备上报名称
     */
    private String localName;
    /**
     * 设备型号
     */
    private String model;
    /**
     * 在线状态：0-不在线，1-在线
     */
    private Integer status;
    /**
     * 具有防护能力的设备布撤防状态：0-睡眠，8-在家，16-外出，普通IPC布撤防状态：0-撤防，1-布防
     */
    private Integer defence;
    /**
     * 是否加密：0-不加密，1-加密
     */
    private Integer isEncrypt;
    /**
     * 告警声音模式：0-短叫，1-长叫，2-静音
     */
    private Integer alarmSoundMode;
    /**
     * 设备下线是否通知：0-不通知 1-通知
     */
    private Integer offlineNotify;
    /**
     * 设备大类
     */
    private String category;
    /**
     * 设备二级类目
     */
    private String parentCategory;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 网络类型
     */
    private String netType;
    /**
     * 信号强度（%）
     */
    private String signal;
    /**
     * 设备风险安全等级，0-安全，大于零，有风险，风险越高，值越大
     */
    private Integer riskLevel;
    /**
     * 设备ip地址
     */
    private String netAddress;

    private Date createTime;

    private Integer pageNo;

    private Integer pageSize;

    private String userMobile;

    private String userName;

    private List<ChildDeviceParam> childDeviceList;

    private Long bedId;

    private Long roomId;

    private Long elderUserRefId;
}
