package com.byzp.platform.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

@Data
public class DeviceConfigDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -6869621172286337477L;
    /**
     * 设备序列号
     */
    private String deviceSerial;
    /**
     * 睡眠监测开始时间 HH:mm
     */
    private String smStartTime;
    /*
     * 睡眠监测结束时间 HH:mm
     */
    private String smEndTime;
    /*
     * 设备版本
     */
    private String version;
    /**
     * 心率异常告警最高
     */
    private Integer xlWarnMax;
    /**
     * 心率异常告警最低
     */
    private Integer xlWarnMin;
    /*
     * 呼吸异常告警最高
     */
    private Integer hxWarnMax;
    /*
     * 呼吸异常告警最低
     */
    private Integer hxWarnMin;
    /*
     * 离床告警时间，单位分钟
     */
    private Integer lcWarnTime;


}
