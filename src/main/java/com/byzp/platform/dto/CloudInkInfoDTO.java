package com.byzp.platform.dto;

import com.byzp.platform.mapper.po.CloudDevice;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class CloudInkInfoDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = -2712456770185590700L;

    private Long id;
    /**
     * 数据库表字段: ink_name, 墨水屏名称
     */
    private String inkName;
    /**
     * 数据库表字段: cloud_application_id, 应用id
     */
    private Long cloudApplicationId;
    /**
     * 墨水屏配置信息(下发给硬件的模版)
     */
    private String settingInfo;
    /**
     * 墨水屏配置信息（供前端回显）
     */
    private String sourceSettingInfo;
    /**
     * 数据库表字段: create_time
     */
    private Date createTime;
    /**
     * 关联的设备数
     */
    private Integer refDeviceNum;

    private List<CloudDevice> refDeviceList;
}
