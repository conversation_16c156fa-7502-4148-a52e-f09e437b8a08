package com.byzp.platform.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class CloudLocationGroupDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 4898704451853967850L;

    private Long id;
    /**
     * 位置id
     */
    private Long locationId;
    /**
     * 分组名称
     */
    private String name;
    /**
     * 备注
     */
    private String remark;

    private Date createTime;
    /**
     * 所属位置名称
     */
    private String locationName;
    /**
     * 设备数
     */
    private Integer deviceNum;

}
