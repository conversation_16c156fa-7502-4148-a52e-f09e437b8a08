package com.byzp.platform.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

@Data
public class DeviceVarietyDTO implements Serializable {
    @Serial
    private static final long serialVersionUID = 3216078189273057370L;

    private Long id;
    /**
     * 名称
     */
    private String name;
    /**
     * 品牌
     */
    private String brand;
    /**
     * 型号
     */
    private String model;
    /**
     * 图片链接
     */
    private String picUrl;

    private Integer isDelete;

    private Date createTime;

    private Integer linkType;

    private Integer type;
}
