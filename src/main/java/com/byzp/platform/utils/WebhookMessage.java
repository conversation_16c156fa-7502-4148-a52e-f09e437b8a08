package com.byzp.platform.utils;

import java.io.Serializable;


public class WebhookMessage implements Serializable {

    /**
     * 消息头
     */
    private WebhookMessageHeader header;

    /**
     * 消息体
     */
    private Object body;

    public WebhookMessage(WebhookMessageHeader header, Object body) {
        this.header = header;
        this.body = body;
    }

    public WebhookMessage() {
    }

    public WebhookMessageHeader getHeader() {
        return header;
    }

    public void setHeader(WebhookMessageHeader header) {
        this.header = header;
    }

    public Object getBody() {
        return body;
    }

    public void setBody(Object body) {
        this.body = body;
    }

    public class WebhookMessageHeader{

        /**
         * 消息id
         */
        private String messageId;

        /**
         * 设备序列号
         */
        private String deviceId;

        /**
         * 消息类型，需向消息管道服务申请
         */
        private String type;

        /**
         * 通道号
         */
        private Integer channelNo;

        /**
         * 消息推送时间
         */
        private Long messageTime;

        public WebhookMessageHeader() {
        }

        public String getMessageId() {
            return messageId;
        }

        public void setMessageId(String messageId) {
            this.messageId = messageId;
        }

        public String getDeviceId() {
            return deviceId;
        }

        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type;
        }

        public Integer getChannelNo() {
            return channelNo;
        }

        public void setChannelNo(Integer channelNo) {
            this.channelNo = channelNo;
        }

        public Long getMessageTime() {
            return messageTime;
        }

        public void setMessageTime(Long messageTime) {
            this.messageTime = messageTime;
        }
    }
}
