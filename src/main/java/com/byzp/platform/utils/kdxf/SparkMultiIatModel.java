package com.byzp.platform.utils.kdxf;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.client.CallbackClient;
import com.byzp.platform.utils.AudioConverter;
import com.byzp.platform.utils.SpringContextUtil;
import com.google.gson.Gson;
import javazoom.jl.decoder.*;
import javazoom.jl.player.Player;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.bouncycastle.asn1.util.ASN1Dump;
import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;

import javax.sound.sampled.AudioFormat;
import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
public class SparkMultiIatModel extends WebSocketListener {
    private static final String hostUrl = "https://iat.cn-huabei-1.xf-yun.com/v1"; // 注意多语种识别，也支持中文音频
    private static final String appid = "4291d1df"; //在控制台-我的应用获取
    private static final String apiSecret = "ODM5M2MyZDM5M2JhZWJmZTk0MDAyOWQ1"; // 在控制台-我的应用获取
    private static final String apiKey = "33d4dc3e322754746476a18c1504a848"; // 在控制台-我的应用获取
    public static final int StatusFirstFrame = 0;
    public static final int StatusContinueFrame = 1;
    public static final int StatusLastFrame = 2;
    public static final Gson gson = new Gson();
    // 开始时间
    private static Date dateBegin = new Date();
    // 结束时间
    private static Date dateEnd = new Date();
    private byte[] voiceByteArr;
    // 设备序列号
    private String deviceSerial;
    // 声纹id
    private String voiceId;
    // 回调地址url
    private String callbackUrl;

    private String msgId;
    /**
     * 语音的mp3 base64
     */
    private String mp3Base64;
    /**
     * 消息类型
     */
    private String msgType;
    /**
     * 消息时间
     */
    private Long msgTime;
    // 暂时没用
    private String pcmPath;
    // 整段语音的识别结果，使用实例变量存储
    private StringBuilder audioText = new StringBuilder();
    // 声音匹配度
    private BigDecimal voiceSimilarity;
    /**
     * 随机数
     */
    private String randNum;
    private static final SimpleDateFormat sdf = new SimpleDateFormat("yyy-MM-dd HH:mm:ss.SSS");

    public SparkMultiIatModel(byte[] voiceInfo, String deviceSerial, String voiceId, String callbackUrl, String msgId,
                              String mp3Base64, String msgType, Long msgTime, String pcmPath, BigDecimal voiceSimilarity,
                              String randNum){
        this.voiceByteArr = voiceInfo;
        this.deviceSerial = deviceSerial;
        this.voiceId = voiceId;
        this.callbackUrl = callbackUrl;
        this.msgId = msgId;
        this.msgType = msgType;
        this.mp3Base64 = mp3Base64;
        this.msgTime = msgTime;
        this.pcmPath = pcmPath;
        this.voiceSimilarity = voiceSimilarity;
        this.randNum = randNum;
    }

    @Override
    public void onOpen(WebSocket webSocket, Response response) {
        super.onOpen(webSocket, response);
        new Thread(() -> {
            //连接成功，开始发送数据
            int frameSize = 1280; //每一帧音频的大小,建议每 40ms 发送 122B
            int intervel = 40;
            int status = 0;  // 音频的状态
            int seq = 0; //数据序号
            try (InputStream fs = new ByteArrayInputStream(voiceByteArr)) {
                byte[] buffer = new byte[frameSize];
                // 发送音频
                end:
                while (true) {
                    seq++; // 每次循环更新下seq
                    int len = fs.read(buffer);
                    if (len == -1) {
                        status = StatusLastFrame;  //文件读完，改变status 为 2
                    }
                    switch (status) {
                        case StatusFirstFrame:   // 第一帧音频status = 0
                            String json = "{\n" +
                                    "  \"header\": {\n" +
                                    "    \"app_id\": \"" + appid + "\",\n" +
                                    "    \"status\": " + StatusFirstFrame + "\n" +
                                    "  },\n" +
                                    "  \"parameter\": {\n" +
                                    "    \"iat\": {\n" +
                                    "      \"domain\": \"slm\",\n" +
                                    "      \"language\": \"zh_cn\",\n" +
                                    "      \"accent\": \"mulacc\",\n" +
                                    "      \"eos\": 6000,\n" +
                                    "      \"vinfo\": 1,\n" +
                                    "      \"result\": {\n" +
                                    "        \"encoding\": \"utf8\",\n" +
                                    "        \"compress\": \"raw\",\n" +
                                    "        \"format\": \"json\"\n" +
                                    "      }\n" +
                                    "    }\n" +
                                    "  },\n" +
                                    "  \"payload\": {\n" +
                                    "    \"audio\": {\n" +
                                    "      \"encoding\": \"raw\",\n" +
                                    "      \"sample_rate\": 16000,\n" +
                                    "      \"channels\": 1,\n" +
                                    "      \"bit_depth\": 16,\n" +
                                    "      \"seq\": " + seq + ",\n" +
                                    "      \"status\": 0,\n" +
                                    "      \"audio\": \"" + Base64.getEncoder().encodeToString(Arrays.copyOf(buffer, len)) + "\"\n" +
                                    "    }\n" +
                                    "  }\n" +
                                    "}";
                            webSocket.send(json);
                            // System.err.println(json);
                            log.info("第一帧音频发送完毕...");
                            log.info("中间音频将持续发送...");
                            status = StatusContinueFrame;  // 发送完第一帧改变status 为 1
                            break;
                        case StatusContinueFrame:  //中间帧status = 1
                            json = "{\n" +
                                    "  \"header\": {\n" +
                                    "    \"app_id\": \"" + appid + "\",\n" +
                                    "    \"status\": 1\n" +
                                    "  },\n" +
                                    "  \"payload\": {\n" +
                                    "    \"audio\": {\n" +
                                    "      \"encoding\": \"raw\",\n" +
                                    "      \"sample_rate\": 16000,\n" +
                                    "      \"channels\": 1,\n" +
                                    "      \"bit_depth\": 16,\n" +
                                    "      \"seq\": " + seq + ",\n" +
                                    "      \"status\": 1,\n" +
                                    "      \"audio\": \"" + Base64.getEncoder().encodeToString(Arrays.copyOf(buffer, len)) + "\"\n" +
                                    "    }\n" +
                                    "  }\n" +
                                    "}";
                            webSocket.send(json);
                            // System.err.println(json);
                            // log.info("中间帧音频发送中...");
                            break;
                        case StatusLastFrame:    // 最后一帧音频status = 2 ，标志音频发送结束
                            json = "{\n" +
                                    "  \"header\": {\n" +
                                    "    \"app_id\": \"" + appid + "\",\n" +
                                    "    \"status\": 2\n" +
                                    "  },\n" +
                                    "  \"payload\": {\n" +
                                    "    \"audio\": {\n" +
                                    "      \"encoding\": \"raw\",\n" +
                                    "      \"sample_rate\": 16000,\n" +
                                    "      \"channels\": 1,\n" +
                                    "      \"bit_depth\": 16,\n" +
                                    "      \"seq\": " + seq + ",\n" +
                                    "      \"status\": 2,\n" +
                                    "      \"audio\": \"\"\n" +
                                    "    }\n" +
                                    "  }\n" +
                                    "}";
                            webSocket.send(json);
                            // System.err.println(json);
                            log.info("最后一帧音频发送完毕...");
                            break end;
                    }
                    Thread.sleep(intervel); //模拟音频采样延时
                }
                log.info("所有音频发送完毕...");
            } catch (FileNotFoundException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }).start();
    }

    @Override
    public void onMessage(WebSocket webSocket, String text) {
        super.onMessage(webSocket, text);
        JsonParse jsonParse = gson.fromJson(text, JsonParse.class);
        if (jsonParse != null) {
            if (jsonParse.header.code != 0) {
                log.info("code=>" + jsonParse.header.code + " error=>" + jsonParse.header.message + " sid=" + jsonParse.header.sid);
                log.info("错误码查询链接：https://www.xfyun.cn/document/error-code");
                return;
            }
            if (jsonParse.payload != null) {
                if (jsonParse.payload.result.text != null) { // 中间结果
                    byte[] decodedBytes = Base64.getDecoder().decode(jsonParse.payload.result.text);
                    String decodeRes = new String(decodedBytes, StandardCharsets.UTF_8);
                    // log.info("中间识别结果 ==》" + decodeRes);
                    JsonParseText jsonParseText = gson.fromJson(decodeRes, JsonParseText.class);
                    List<Ws> wsList = jsonParseText.ws;
                    System.out.print("中间识别结果==》");
                    for (Ws ws : wsList) {
                        List<Cw> cwList = ws.cw;
                        for (Cw cw : cwList) {
                            System.out.print(cw.w);
                            audioText.append(cw.w);
                        }
                    }
                }
                if (jsonParse.payload.result.status == 2) { // 最终结果  说明数据全部返回完毕，可以关闭连接，释放资源
                    log.info("session end ");
                    dateEnd = new Date();
                    log.info(sdf.format(dateBegin) + "开始");
                    log.info(sdf.format(dateEnd) + "结束");
                    log.info("耗时:" + (dateEnd.getTime() - dateBegin.getTime()) + "ms");
                     log.info("最终识别结果 ==》" + audioText);  // 按照规则替换与追加出最终识别结果
                    log.info("本次识别sid ==》" + jsonParse.header.sid);
                    doCallback(audioText.toString());
                    webSocket.close(1000, "");
                }
            }
        }
    }

    public void doCallback(String audioText){
        JSONObject param = new JSONObject();
        param.put("msgId", msgId);
        param.put("msgType", msgType);
        param.put("msgTime", msgTime);
        param.put("deviceSerial", deviceSerial);
        JSONObject data = new JSONObject();
        data.put("audioText", audioText);
        data.put("voiceId", voiceId);
        data.put("mp3Base64", mp3Base64);
        data.put("voiceSimilarity", voiceSimilarity.toString());
        data.put("randNum", randNum);
        param.put("data", data);
        CallbackClient callbackClient = (CallbackClient) SpringContextUtil.getBean("callbackClient");
        String callback = callbackClient.callback(callbackUrl, param.toJSONString(), "byzp");
        log.info("声纹回调应用结果:" + callback);
    }
    @Override
    public void onFailure(WebSocket webSocket, Throwable t, Response response) {
        super.onFailure(webSocket, t, response);
        try {
            if (null != response) {
                int code = response.code();
                log.info("onFailure code:" + code);
                log.info("onFailure body:" + response.body().string());
                if (101 != code) {
                    log.info("connection failed");
                    System.exit(0);
                }
            }
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

//    public static void main(String[] args) throws Throwable {
//        // 构建鉴权url
//        String authUrl = KdxfUtil.getAuthUrl();
//        OkHttpClient client = new OkHttpClient.Builder().build();
//        //将url中的 schema http://和https://分别替换为ws:// 和 wss://
//        String url = authUrl.toString().replace("http://", "ws://").replace("https://", "wss://");
//        //log.info(url);
//        Request request = new Request.Builder().url(url).build();
//        // log.info(client.newCall(request).execute());
//        //log.info("url===>" + url);
//        String filePath = "D:\\spark_mucl_cn_iat_demo_java\\resource\\iat\\76633606-628.mp3";
//        String targetPath = "D:\\spark_mucl_cn_iat_demo_java\\resource\\iat\\copy.pcm";
//        String mp3Base64 = audioFileToBase64(filePath);
//        AudioConverter.convertMp3Base64ToPcmFile(mp3Base64, targetPath);
////        byte[] pcmBytes = AudioConverter.convertMp3ToPcm(mp3Base64);
////        byte[] pcmBytes = convertMp3FileToPcm(filePath);
////        String originBase64 = audioFileToBase64(targetPath);
//        String originBase64 = audioFileToBase64(targetPath);
//        System.out.println(originBase64.substring(0,1000));
//        byte[] originBytes = decodeBase64ToByteArray(originBase64);//--这个originBytes没问题
////        byte[] pcmBytes = AudioConverter.getPcmBase64FromMp3Byte(originBytes);
////        convertMp3Base64ToPcmFile(originBase64, targetPath);
//        WebSocket webSocket = client.newWebSocket(request, new SparkMultiIatModel(originBytes,"CD6785455","","","","","pcmBytes", null,""));
//
////        convertMP3ToPCM(filePath, targetPath);
//    }

    public static void convertMp3Base64ToPcmFile(String mp3Base64, String pcmFilePath) throws IOException, InterruptedException {
        // 解码 Base64 数据
        byte[] mp3Bytes = Base64.getDecoder().decode(mp3Base64);

        // 构建 FFmpeg 命令
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-f", "mp3",
                "-i", "-",
                "-f", "s16le",
                "-ac", "1",
                "-ar", "16000",
                pcmFilePath
        );

        // 启动 FFmpeg 进程
        Process process = processBuilder.start();

        // 获取 FFmpeg 进程的输入流
        OutputStream ffmpegInput = process.getOutputStream();
        ffmpegInput.write(mp3Bytes);
        ffmpegInput.close();

        // 获取 FFmpeg 进程的错误流
        InputStream errorStream = process.getErrorStream();
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(errorStream));
        String line;
        while ((line = errorReader.readLine()) != null) {
            System.err.println(line);
        }

        // 等待 FFmpeg 进程完成
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("FFmpeg 进程执行失败，退出码: " + exitCode);
        }
    }

    public static void convertMP3ToPCM(String mp3FilePath, String pcmFilePath) {
        try {
            // 构建 FFmpeg 命令
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "ffmpeg",
                    "-i", mp3FilePath,
                    "-f", "s16le",
                    "-ac", "1",
                    "-ar", "16000",
                    pcmFilePath
            );

            // 启动 FFmpeg 进程
            Process process = processBuilder.start();

            // 等待进程执行完成
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                System.out.println("MP3 文件已成功转换为 PCM 文件。");
            } else {
                System.err.println("转换过程中出现错误，退出码: " + exitCode);
            }
        } catch (IOException | InterruptedException e) {
            System.err.println("执行过程中出现异常: " + e.getMessage());
        }
    }

    /**
     * 这个方法没问题
     * @param filePath
     * @return
     */
    public static String audioFileToBase64(String filePath) {
        File file = new File(filePath);
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            byte[] bytes = new byte[(int) file.length()];
            fileInputStream.read(bytes);
            return Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 这个方法没问题
     * @param base64Encoded
     * @return
     */
    public static byte[] decodeBase64ToByteArray(String base64Encoded){
        Base64.Decoder decoder = Base64.getDecoder();
        return decoder.decode(base64Encoded);
    }
    // 返回结果拆分与展示，仅供参考
    // 返回结果拆分与展示，仅供参考
    class JsonParse {
        Header header;
        Payload payload;
    }

    class Header {
        int code;
        String message;
        String sid;
        int status;
    }

    class Payload {
        Result result;
    }

    class Result {
        String text;
        int status;
    }

    class JsonParseText {
        List<Ws> ws;
        String pgs;
        List<Integer> rg;
    }

    class Ws {
        List<Cw> cw;
    }

    class Cw {
        String w;
    }
}
