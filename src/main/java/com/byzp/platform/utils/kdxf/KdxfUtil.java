package com.byzp.platform.utils.kdxf;

import okhttp3.HttpUrl;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.net.URL;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 科大讯飞工具类
 */
public class KdxfUtil {

    private static final String hostUrl = "https://iat.cn-huabei-1.xf-yun.com/v1"; // 注意多语种识别，也支持中文音频
    private static final String appid = "4291d1df"; //在控制台-我的应用获取
    private static final String apiSecret = "ODM5M2MyZDM5M2JhZWJmZTk0MDAyOWQ1"; // 在控制台-我的应用获取
    private static final String apiKey = "33d4dc3e322754746476a18c1504a848"; // 在控制台-我的应用获取
    private static String requestUrl = "https://api.xf-yun.com/v1/private/s782b4996";// 声纹识别

    public static String getAuthUrl() throws Exception {
        URL url = new URL(hostUrl);
        SimpleDateFormat format = new SimpleDateFormat("EEE, dd MMM yyyy HH:mm:ss z", Locale.US);
        format.setTimeZone(TimeZone.getTimeZone("GMT"));
        String date = format.format(new Date());
        StringBuilder builder = new StringBuilder("host: ").append(url.getHost()).append("\n").//
                append("date: ").append(date).append("\n").//
                append("GET ").append(url.getPath()).append(" HTTP/1.1");
        // System.out.println(builder);
        Charset charset = Charset.forName("UTF-8");
        Mac mac = Mac.getInstance("hmacsha256");
        SecretKeySpec spec = new SecretKeySpec(apiSecret.getBytes(charset), "hmacsha256");
        mac.init(spec);
        byte[] hexDigits = mac.doFinal(builder.toString().getBytes(charset));
        String sha = Base64.getEncoder().encodeToString(hexDigits);

        //System.out.println(sha);
        String authorization = String.format("api_key=\"%s\", algorithm=\"%s\", headers=\"%s\", signature=\"%s\"", apiKey, "hmac-sha256", "host date request-line", sha);
        //System.out.println(authorization);
        HttpUrl httpUrl = HttpUrl.parse("https://" + url.getHost() + url.getPath()).newBuilder().//
                addQueryParameter("authorization", Base64.getEncoder().encodeToString(authorization.getBytes(charset))).//
                addQueryParameter("date", date).//
                addQueryParameter("host", url.getHost()).//
                build();
        return httpUrl.toString();
    }

    /**
     * 新建声纹分组信息
     * @param groupId 分组id（取应用id即appId）
     * @param groupName 分组名称
     * @param groupInfo 分组详细信息
     */
    public static void createGroup(String groupId, String groupName, String groupInfo){
        CreateGroup.doCreateGroup(requestUrl, appid, apiSecret, apiKey, groupId, groupName, groupInfo);
    }

    /**
     * 录入声纹信息
     * @param groupId 分组id
     * @param featureId 声纹业务id
     * @param featureInfo 声纹描述
     * @param voiceBase64 声纹语音的base64
     */
    public static void createFeature(String groupId, String featureId, String featureInfo, String voiceBase64){
        CreateFeature.doCreateFeature(requestUrl, appid, apiSecret, apiKey, null, groupId, featureId, featureInfo, voiceBase64);
    }

    /**
     * 更新声纹信息
     * @param groupId 分组id
     * @param featureId 声纹业务id
     * @param featureInfo 声纹描述
     * @param voiceBase64 声纹语音的base64
     */
    public static void updateFeature(String groupId, String featureId, String featureInfo, String voiceBase64){
        UpdateFeature.doUpdateFeature(requestUrl, appid, apiSecret, apiKey, null, groupId, featureId, featureInfo, voiceBase64);
    }

    /**
     * 删除声纹信息
     * @param groupId 分组id
     * @param featureId 声纹业务id
     */
    public static void deleteFeature(String groupId, String featureId){
        DeleteFeature.doDeleteFeature(requestUrl, appid, apiSecret, apiKey, groupId, featureId);
    }

    /**
     * 查询声纹列表
     * @param groupId
     * @param voiceBase64
     * @return
     */
    public static List<ScoreInfo> searchFeatures(String groupId, String voiceBase64){
        return SearchFeature.doSearchFeature(requestUrl, appid, apiSecret, apiKey, null, groupId, voiceBase64);
    }
}
