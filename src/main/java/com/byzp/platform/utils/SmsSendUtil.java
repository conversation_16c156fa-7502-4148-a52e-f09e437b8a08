package com.byzp.platform.utils;


import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;

import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.byzp.platform.dto.SmsSendResultDTO;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * @Author: qxl
 * @Date: 2021/9/14 13:24
 */
@Data
@Slf4j
public class SmsSendUtil {
    private static final String APP_KEY = "LTAIBFSQrgOiVkFb";
    private static final String APP_KEY_SECRET = "aVdUJJQzlboDFLXmeZyu5v11J5hfA3";
    private static final String SIGN_NAME = "小品出行";


    public static SmsSendResultDTO sendSms(String mobile, String param, String template) {
        SendSmsResponse sendSmsResponse = null;
        try {
            //设置超时时间-可自行调整
            System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
            System.setProperty("sun.net.client.defaultReadTimeout", "10000");
            //初始化ascClient需要的几个参数
            //短信API产品名称（短信产品名固定，无需修改）
            final String product = "Dysmsapi";
            //短信API产品域名（接口地址固定，无需修改）
            final String domain = "dysmsapi.aliyuncs.com";
            //初始化ascClient,暂时不支持多region（请勿修改）
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", APP_KEY,
                    APP_KEY_SECRET);
            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", product, domain);
            IAcsClient acsClient = new DefaultAcsClient(profile);
            //组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            //使用post提交
            request.setMethod(MethodType.POST);
            //必填:待发送手机号。支持以逗号分隔的形式进行批量调用，批量上限为1000个手机号码,批量调用相对于单条调用及时性稍有延迟,验证码类型的短信推荐使用单条调用的方式
            request.setPhoneNumbers(mobile);
            //必填:短信签名-可在短信控制台中找到
            request.setSignName(SIGN_NAME);
            //必填:短信模板-可在短信控制台中找到
            request.setTemplateCode(template);
            //可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时,此处的值为
            //友情提示:如果JSON中需要带换行符,请参照标准的JSON协议对换行符的要求,比如短信内容中包含\r\n的情况在JSON中需要表示成\\r\\n,否则会导致JSON在服务端解析失败
            request.setTemplateParam(param);
            //可选-上行短信扩展码(扩展码字段控制在7位或以下，无特殊需求用户请忽略此字段)
            //request.setSmsUpExtendCode("90997");
            //可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
            request.setOutId("1");
            //请求失败这里会抛ClientException异常
            sendSmsResponse = acsClient.getAcsResponse(request);
            log.info("sms send result=" + JSONObject.toJSONString(sendSmsResponse));
            if (sendSmsResponse.getCode() != null) {
                //请求成功
                if (sendSmsResponse.getCode().equals("OK")) {
                    return new SmsSendResultDTO(sendSmsResponse);
                }
                if (sendSmsResponse.getCode().equals("isv.BUSINESS_LIMIT_CONTROL")) {
                    return new SmsSendResultDTO(false, "短信超过1条/分钟或5条/小时或累计10条/天", sendSmsResponse);
                }
            }
        } catch (Exception e) {
            log.error("短信发送失败", e);
        }
        return new SmsSendResultDTO(false, "短信发送失败", sendSmsResponse);
    }
}
