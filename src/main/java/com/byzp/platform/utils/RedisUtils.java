package com.byzp.platform.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.concurrent.TimeUnit;


@Slf4j
@Component
public class RedisUtils {

    //操作订单
    public static final String doOrder="lock_do_order";

    //订单自动失效 key
    public static final String needExpireOrder="need_expire_order";

    //监听锁失效
    public static final String expireEvent="expire_event_";


    @Autowired
    private RedisTemplate redisTemplate;


    @Value("${spring.redis.app}")
    public String redisApp;

    //处理一个redis key  使它变成当前系统的专有key
    public String getRedisAppKey(String key){
        return redisApp+"_"+key;
    }

    /**
     * 分布式加锁是否成功
     *
     * @param key  key值
     * @param time 过期时间
     * @return
     */

    public Boolean addLock(String key, long time) {
        Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(key, key, time, TimeUnit.SECONDS);
        return aBoolean;
    }

    public Boolean addLock(String key, long time, TimeUnit timeUnit) {
        Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(key, key, time, timeUnit);
        return aBoolean;
    }

//    public Boolean addLock(String key,String value,long time) {
//        Boolean aBoolean = redisTemplate.opsForValue().setIfAbsent(key, value, time, TimeUnit.SECONDS);
//        return aBoolean;
//    }


    /**
     * 分布式加锁是否成功
     *
     * @param key     key值
     * @param time    锁过期时间 秒 默认10秒
     * @param timeOut 加锁超时时间 毫秒 默认15秒
     * @return
     */

    public void addSynchronizeLock(String key, Long time, Long timeOut) throws Exception {
        if(null==time){
            time=10l;
        }
        if(null==timeOut){
            timeOut=15000l;
        }
        long end = System.currentTimeMillis() + timeOut;
        while (!addLock(key, time)) {
            Thread.sleep(100);
            if (System.currentTimeMillis() > end) {
                //如果超出了加锁超时时间 则认为加锁失败 放弃加锁
                log.error("分布式锁加锁失败 {}",key);
                return;
            }
        }
    }



//    /**
//     * 分布式加锁是否成功
//     *
//     * @param key     key值
//     * @param value     value值
//     * @param time    锁过期时间 秒 默认7秒
//     * @param timeOut 加锁超时时间 毫秒 默认10秒
//     * @return
//     */
//
//    public void addSynchronizeLock(String key,String value, Long time, Long timeOut) throws Exception {
//        if(null==time){
//            time=7l;
//        }
//        if(null==timeOut){
//            timeOut=10000l;
//        }
//        long end = System.currentTimeMillis() + timeOut;
//        while (!addLock(key,value, time)) {
//            Thread.sleep(100);
//            if (System.currentTimeMillis() > end) {
//                //如果超出了加锁超时时间 则认为加锁失败
//                throw new KylinBusinessException(ErrorCodes.SYSTEM_BUSY);
//            }
//        }
//    }

    /**
     * @param key  键
     * @param time 时间(秒)
     * @return
     * @Description 指定缓存失效时间
     */

    public boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("设置过期时间失败", e);
            return false;
        }

    }


    /**
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效
     * @Description 根据key 获取过期时间
     */
    public long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }


    /**
     * @param key 键
     * @return true 存在 false不存在
     * @Description 判断key是否存在
     */
    public boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("判断key是否存在失败", e);
            return false;
        }
    }


    /**
     * @param key 可以传一个值 或多个
     * @Description 删除缓存
     */

    @SuppressWarnings("unchecked")
    public void del(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete(CollectionUtils.arrayToList(key));
            }
        }
    }

    /**
     * @param key 键
     * @return 值
     * @Description 普通缓存获取
     */
    public Object get(String key) {
        return key == null ? null : redisTemplate.opsForValue().get(key);
    }


    /**
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     * @Description 普通缓存放入
     */
    public boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("保存失败", e);
            return false;
        }
    }


    /**
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于 如果time小于等于 将设置无限期
     * @return true成功 false 失败
     * @Description 普通缓存放入并设置时间
     */
    public boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            log.error("保存失败", e);
            return false;
        }
    }


    /**
     * @param key   键
     * @param value 值
     * @param time  具体的时间
     * @return true成功 false 失败
     * @Description 普通缓存放入并设置时间
     */
    public boolean set(String key, Object value, Date time) {
        try {
            redisTemplate.opsForValue().set(key,value);
            redisTemplate.expireAt(key,time);
            return true;
        } catch (Exception e) {
            log.error("保存失败", e);
            return false;
        }
    }



}
