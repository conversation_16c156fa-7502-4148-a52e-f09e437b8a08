package com.byzp.platform.utils;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
@Slf4j
public class MqttUtil {

    @Resource
    private MqttClient mqttClient;

    public void sendMsgToDevice(String deviceSerial, String content) {
        String topic = "xpky/" + deviceSerial;
        MqttMessage message = new MqttMessage(content.getBytes());
        message.setQos(2);
        try {
            mqttClient.publish(topic, message);
            log.info("发送消息成功：" + content);
        }catch (Exception e){
            log.info("mqtt消息发送失败:" + content, e);
        }
    }
}
