package com.byzp.platform.utils;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.dto.DeviceConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 设备配置查询等待管理器
 * 用于管理设备配置查询的异步等待机制
 */
@Component
@Slf4j
public class DeviceConfigWaitManager {

    /**
     * 存储等待中的设备配置查询请求
     * key: deviceSerial, value: CompletableFuture<DeviceConfigDTO>
     */
    private final ConcurrentHashMap<String, CompletableFuture<DeviceConfigDTO>> waitingRequests = new ConcurrentHashMap<>();

    /**
     * 默认超时时间（秒）
     */
    private static final int DEFAULT_TIMEOUT_SECONDS = 30;

    /**
     * 创建一个等待设备配置响应的Future
     * 
     * @param deviceSerial 设备序列号
     * @param timeoutSeconds 超时时间（秒）
     * @return CompletableFuture<DeviceConfigDTO>
     */
    public CompletableFuture<DeviceConfigDTO> createWaitingRequest(String deviceSerial, int timeoutSeconds) {
        CompletableFuture<DeviceConfigDTO> future = new CompletableFuture<>();
        
        // 设置超时处理
        future.orTimeout(timeoutSeconds, TimeUnit.SECONDS)
                .whenComplete((result, throwable) -> {
                    // 无论成功还是失败，都要清理等待列表
                    waitingRequests.remove(deviceSerial);
                    if (throwable != null) {
                        log.warn("设备配置查询超时或失败，deviceSerial: {}, error: {}", deviceSerial, throwable.getMessage());
                    }
                });
        
        // 如果已经有相同设备的等待请求，取消之前的请求
        CompletableFuture<DeviceConfigDTO> existingFuture = waitingRequests.put(deviceSerial, future);
        if (existingFuture != null && !existingFuture.isDone()) {
            existingFuture.cancel(true);
            log.info("取消之前的设备配置查询请求，deviceSerial: {}", deviceSerial);
        }
        
        log.info("创建设备配置查询等待请求，deviceSerial: {}, timeout: {}秒", deviceSerial, timeoutSeconds);
        return future;
    }

    /**
     * 创建一个等待设备配置响应的Future（使用默认超时时间）
     * 
     * @param deviceSerial 设备序列号
     * @return CompletableFuture<DeviceConfigDTO>
     */
    public CompletableFuture<DeviceConfigDTO> createWaitingRequest(String deviceSerial) {
        return createWaitingRequest(deviceSerial, DEFAULT_TIMEOUT_SECONDS);
    }

    /**
     * 完成等待中的设备配置查询请求
     * 
     * @param deviceSerial 设备序列号
     * @param configData 设备配置数据
     * @return 是否成功完成请求
     */
    public boolean completeRequest(String deviceSerial, DeviceConfigDTO configData) {
        CompletableFuture<DeviceConfigDTO> future = waitingRequests.get(deviceSerial);
        if (future != null && !future.isDone()) {
            boolean completed = future.complete(configData);
            if (completed) {
                log.info("成功完成设备配置查询请求，deviceSerial: {}", deviceSerial);
            }
            return completed;
        }
        log.warn("未找到等待中的设备配置查询请求，deviceSerial: {}", deviceSerial);
        return false;
    }

    /**
     * 取消等待中的设备配置查询请求
     * 
     * @param deviceSerial 设备序列号
     * @return 是否成功取消请求
     */
    public boolean cancelRequest(String deviceSerial) {
        CompletableFuture<DeviceConfigDTO> future = waitingRequests.remove(deviceSerial);
        if (future != null && !future.isDone()) {
            boolean cancelled = future.cancel(true);
            if (cancelled) {
                log.info("成功取消设备配置查询请求，deviceSerial: {}", deviceSerial);
            }
            return cancelled;
        }
        return false;
    }

    /**
     * 获取当前等待中的请求数量
     * 
     * @return 等待中的请求数量
     */
    public int getWaitingRequestCount() {
        return waitingRequests.size();
    }

    /**
     * 检查是否有等待中的请求
     * 
     * @param deviceSerial 设备序列号
     * @return 是否有等待中的请求
     */
    public boolean hasWaitingRequest(String deviceSerial) {
        CompletableFuture<DeviceConfigDTO> future = waitingRequests.get(deviceSerial);
        return future != null && !future.isDone();
    }

    /**
     * 解析MQTT消息中的设备配置数据
     * 
     * @param payloadObject MQTT消息的payload
     * @return DeviceConfigDTO
     */
    public DeviceConfigDTO parseDeviceConfig(JSONObject payloadObject) {
        try {
            DeviceConfigDTO configDTO = new DeviceConfigDTO();
            
            // 设置设备序列号
            configDTO.setDeviceSerial(payloadObject.getString("clientId"));
            
            // 解析payload中的配置信息
            JSONObject payload = payloadObject.getJSONObject("payload");
            if (payload != null) {
                // 解析睡眠监测时间
                if (payload.containsKey("SM_Starttime")) {
                    Integer startTimeSeconds = payload.getInteger("SM_Starttime");
                    if (startTimeSeconds != null) {
                        configDTO.setSmStartTime(formatSecondsToTime(startTimeSeconds));
                    }
                }
                
                if (payload.containsKey("SM_Endtime")) {
                    Integer endTimeSeconds = payload.getInteger("SM_Endtime");
                    if (endTimeSeconds != null) {
                        configDTO.setSmEndTime(formatSecondsToTime(endTimeSeconds));
                    }
                }
                
                // 解析其他配置项
                configDTO.setVersion(payload.getString("SoftwareVersion"));
                configDTO.setXlWarnMax(payload.getInteger("xl_warn_max"));
                configDTO.setXlWarnMin(payload.getInteger("xl_warn_min"));
                configDTO.setHxWarnMax(payload.getInteger("hx_warn_max"));
                configDTO.setHxWarnMin(payload.getInteger("hx_warn_min"));
                configDTO.setLcWarnTime(payload.getInteger("lc_warn_time"));
            }
            
            return configDTO;
        } catch (Exception e) {
            log.error("解析设备配置数据失败", e);
            return null;
        }
    }

    /**
     * 将秒数转换为时间格式 HH:mm
     * 
     * @param seconds 秒数
     * @return 时间字符串 HH:mm
     */
    private String formatSecondsToTime(int seconds) {
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        return String.format("%02d:%02d", hours, minutes);
    }
}
