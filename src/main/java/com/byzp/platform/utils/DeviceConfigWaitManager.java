package com.byzp.platform.utils;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.dto.DeviceConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 设备配置查询等待管理器
 * 用于管理设备配置查询的异步等待机制
 */
@Component
@Slf4j
public class DeviceConfigWaitManager {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Resource
    private RedisUtils redisUtils;

    /**
     * 存储等待中的设备配置查询请求
     * key: deviceSerial, value: CompletableFuture<DeviceConfigDTO>
     */
    private final ConcurrentHashMap<String, CompletableFuture<DeviceConfigDTO>> waitingRequests = new ConcurrentHashMap<>();

    /**
     * 默认超时时间（秒）
     */
    private static final int DEFAULT_TIMEOUT_SECONDS = 30;

    /**
     * Redis中设备配置响应的key前缀
     */
    private static final String DEVICE_CONFIG_RESPONSE_KEY = "device_config_response:";

    /**
     * Redis中设备配置响应的过期时间（秒）
     */
    private static final int RESPONSE_EXPIRE_SECONDS = 60;

    /**
     * 创建一个等待设备配置响应的Future
     *
     * @param deviceSerial 设备序列号
     * @param timeoutSeconds 超时时间（秒）
     * @return CompletableFuture<DeviceConfigDTO>
     */
    public CompletableFuture<DeviceConfigDTO> createWaitingRequest(String deviceSerial, int timeoutSeconds) {
        // 首先检查Redis中是否已经有响应
        DeviceConfigDTO existingResponse = getResponseFromRedis(deviceSerial);
        if (existingResponse != null) {
            log.info("从Redis中找到已存在的设备配置响应，deviceSerial: {}", deviceSerial);
            return CompletableFuture.completedFuture(existingResponse);
        }

        CompletableFuture<DeviceConfigDTO> future = new CompletableFuture<>();

        // 设置超时处理
        future.orTimeout(timeoutSeconds, TimeUnit.SECONDS)
                .whenComplete((result, throwable) -> {
                    // 无论成功还是失败，都要清理等待列表
                    waitingRequests.remove(deviceSerial);
                    if (throwable != null) {
                        log.warn("设备配置查询超时或失败，deviceSerial: {}, error: {}", deviceSerial, throwable.getMessage());
                    }
                });

        // 如果已经有相同设备的等待请求，取消之前的请求
        CompletableFuture<DeviceConfigDTO> existingFuture = waitingRequests.put(deviceSerial, future);
        if (existingFuture != null && !existingFuture.isDone()) {
            existingFuture.cancel(true);
            log.info("取消之前的设备配置查询请求，deviceSerial: {}", deviceSerial);
        }

        // 启动定期检查Redis的任务
        startPeriodicRedisCheck(deviceSerial, future, timeoutSeconds);

        log.info("创建设备配置查询等待请求，deviceSerial: {}, timeout: {}秒", deviceSerial, timeoutSeconds);
        return future;
    }

    /**
     * 创建一个等待设备配置响应的Future（使用默认超时时间）
     * 
     * @param deviceSerial 设备序列号
     * @return CompletableFuture<DeviceConfigDTO>
     */
    public CompletableFuture<DeviceConfigDTO> createWaitingRequest(String deviceSerial) {
        return createWaitingRequest(deviceSerial, DEFAULT_TIMEOUT_SECONDS);
    }

    /**
     * 完成等待中的设备配置查询请求
     *
     * @param deviceSerial 设备序列号
     * @param configData 设备配置数据
     * @return 是否成功完成请求
     */
    public boolean completeRequest(String deviceSerial, DeviceConfigDTO configData) {
        // 首先将响应存储到Redis中，供其他服务器实例使用
        storeResponseToRedis(deviceSerial, configData);

        // 然后尝试完成本地的等待请求
        CompletableFuture<DeviceConfigDTO> future = waitingRequests.get(deviceSerial);
        if (future != null && !future.isDone()) {
            boolean completed = future.complete(configData);
            if (completed) {
                log.info("成功完成设备配置查询请求，deviceSerial: {}", deviceSerial);
            }
            return completed;
        }
        log.info("本地无等待请求，但已将响应存储到Redis，deviceSerial: {}", deviceSerial);
        return true;
    }

    /**
     * 取消等待中的设备配置查询请求
     * 
     * @param deviceSerial 设备序列号
     * @return 是否成功取消请求
     */
    public boolean cancelRequest(String deviceSerial) {
        CompletableFuture<DeviceConfigDTO> future = waitingRequests.remove(deviceSerial);
        if (future != null && !future.isDone()) {
            boolean cancelled = future.cancel(true);
            if (cancelled) {
                log.info("成功取消设备配置查询请求，deviceSerial: {}", deviceSerial);
            }
            return cancelled;
        }
        return false;
    }

    /**
     * 获取当前等待中的请求数量
     * 
     * @return 等待中的请求数量
     */
    public int getWaitingRequestCount() {
        return waitingRequests.size();
    }

    /**
     * 检查是否有等待中的请求
     * 
     * @param deviceSerial 设备序列号
     * @return 是否有等待中的请求
     */
    public boolean hasWaitingRequest(String deviceSerial) {
        CompletableFuture<DeviceConfigDTO> future = waitingRequests.get(deviceSerial);
        return future != null && !future.isDone();
    }

    /**
     * 解析MQTT消息中的设备配置数据
     * 
     * @param payloadObject MQTT消息的payload
     * @return DeviceConfigDTO
     */
    public DeviceConfigDTO parseDeviceConfig(JSONObject payloadObject) {
        try {
            DeviceConfigDTO configDTO = new DeviceConfigDTO();
            
            // 设置设备序列号
            configDTO.setDeviceSerial(payloadObject.getString("clientId"));
            
            // 解析payload中的配置信息
            JSONObject payload = payloadObject.getJSONObject("payload");
            if (payload != null) {
                // 解析睡眠监测时间
                if (payload.containsKey("SM_Starttime")) {
                    Integer startTimeSeconds = payload.getInteger("SM_Starttime");
                    if (startTimeSeconds != null) {
                        configDTO.setSmStartTime(formatSecondsToTime(startTimeSeconds));
                    }
                }
                
                if (payload.containsKey("SM_Endtime")) {
                    Integer endTimeSeconds = payload.getInteger("SM_Endtime");
                    if (endTimeSeconds != null) {
                        configDTO.setSmEndTime(formatSecondsToTime(endTimeSeconds));
                    }
                }
                
                // 解析其他配置项
                configDTO.setVersion(payload.getString("SoftwareVersion"));
                configDTO.setXlWarnMax(payload.getInteger("xl_warn_max"));
                configDTO.setXlWarnMin(payload.getInteger("xl_warn_min"));
                configDTO.setHxWarnMax(payload.getInteger("hx_warn_max"));
                configDTO.setHxWarnMin(payload.getInteger("hx_warn_min"));
                configDTO.setLcWarnTime(payload.getInteger("lc_warn_time"));
            }
            
            return configDTO;
        } catch (Exception e) {
            log.error("解析设备配置数据失败", e);
            return null;
        }
    }

    /**
     * 将秒数转换为时间格式 HH:mm
     *
     * @param seconds 秒数
     * @return 时间字符串 HH:mm
     */
    private String formatSecondsToTime(int seconds) {
        int hours = seconds / 3600;
        int minutes = (seconds % 3600) / 60;
        return String.format("%02d:%02d", hours, minutes);
    }

    /**
     * 将设备配置响应存储到Redis中
     *
     * @param deviceSerial 设备序列号
     * @param configData 设备配置数据
     */
    private void storeResponseToRedis(String deviceSerial, DeviceConfigDTO configData) {
        try {
            String key = redisUtils.getRedisAppKey(DEVICE_CONFIG_RESPONSE_KEY + deviceSerial);
            redisUtils.set(key, configData, RESPONSE_EXPIRE_SECONDS);
            log.info("设备配置响应已存储到Redis，deviceSerial: {}, key: {}", deviceSerial, key);
        } catch (Exception e) {
            log.error("存储设备配置响应到Redis失败，deviceSerial: {}", deviceSerial, e);
        }
    }

    /**
     * 从Redis中获取设备配置响应
     *
     * @param deviceSerial 设备序列号
     * @return 设备配置数据，如果不存在则返回null
     */
    private DeviceConfigDTO getResponseFromRedis(String deviceSerial) {
        try {
            String key = redisUtils.getRedisAppKey(DEVICE_CONFIG_RESPONSE_KEY + deviceSerial);
            Object response = redisUtils.get(key);
            if (response instanceof DeviceConfigDTO) {
                log.info("从Redis中获取到设备配置响应，deviceSerial: {}", deviceSerial);
                // 获取后删除，避免重复使用
                redisUtils.del(key);
                return (DeviceConfigDTO) response;
            }
        } catch (Exception e) {
            log.error("从Redis中获取设备配置响应失败，deviceSerial: {}", deviceSerial, e);
        }
        return null;
    }

    /**
     * 启动定期检查Redis的任务
     *
     * @param deviceSerial 设备序列号
     * @param future 等待的Future
     * @param timeoutSeconds 超时时间
     */
    private void startPeriodicRedisCheck(String deviceSerial, CompletableFuture<DeviceConfigDTO> future, int timeoutSeconds) {
        // 使用单独的线程定期检查Redis
        Thread checkThread = new Thread(() -> {
            long startTime = System.currentTimeMillis();
            long timeoutMillis = timeoutSeconds * 1000L;

            while (!future.isDone() && (System.currentTimeMillis() - startTime) < timeoutMillis) {
                try {
                    DeviceConfigDTO response = getResponseFromRedis(deviceSerial);
                    if (response != null) {
                        future.complete(response);
                        log.info("通过Redis检查完成设备配置查询请求，deviceSerial: {}", deviceSerial);
                        break;
                    }

                    // 每秒检查一次
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    log.warn("Redis检查线程被中断，deviceSerial: {}", deviceSerial);
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    log.error("Redis检查过程中发生异常，deviceSerial: {}", deviceSerial, e);
                }
            }
        });

        checkThread.setName("DeviceConfigRedisCheck-" + deviceSerial);
        checkThread.setDaemon(true);
        checkThread.start();
    }
}
