package com.byzp.platform.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class ModuloUtil {

    /**
     * 对字体取模
     * @param content 内容
     * @param size 字体大小
     * @return
     */
    public static String readFont(String content, Integer size) {
        // 根据字体大小从不同字库中取模
        String fontFileName = "text" + size + ".fon";
        Map<Long, Long> indexMap = new HashMap<>();
        int fontSize = size * (size/8);
        try (BufferedReader br = new BufferedReader(new InputStreamReader(new ClassPathResource("text_index.txt").getInputStream()))) {
            String line;
            int index = 0;
            while ((line = br.readLine()) != null) {
                char[] parts = line.toCharArray();
                if (parts.length > 10) {
                    for (int i = 0; i < parts.length; i++) {
                        indexMap.put((long) parts[i], (long) index++);
                    }
                }
            }

            byte[] bytes = new byte[content.length() * fontSize];
            for (int i = 0; i < content.length(); i++) {
                char c = content.charAt(i);
                byte[] part = readBytes(fontFileName, indexMap.get((long) c), fontSize);
                System.arraycopy(part, 0, bytes, i * fontSize, fontSize);
            }
//            for (int j = 0; j < bytes.length; j++) {
//                System.out.print(String.format(",0x%02X ", bytes[j]));
//            }
            log.info("base64:" + Base64.getEncoder().encodeToString(bytes));
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.info("字符串取模异常", e);
        }
        return "";
    }

//    public static byte[] readFontData(RandomAccessFile binFile, long offset, int charSize)
//            throws IOException {
////        System.out.println("offset:" + offset);
//        binFile.seek(offset * charSize);
//        byte[] buffer = new byte[charSize];
//        binFile.readFully(buffer);
//        return buffer;
//    }

    private static final String CACHE_DIR = "local-cache"; // 可根据需要修改路径

//    // 获取带缓存的 FileChannel
//    public static FileChannel getFileChannel(String classpathFilePath) throws IOException {
//        File cachedFile = getCachedFile(classpathFilePath);
//        return new FileInputStream(cachedFile).getChannel();
//    }

    // 获取缓存文件（不存在则复制）
    public static File getCachedFile(String classpathFilePath) throws IOException {
        String fileName = new File(classpathFilePath).getName();
        File cacheDir = new File(CACHE_DIR);
        if (!cacheDir.exists()) cacheDir.mkdirs();

        File cachedFile = new File(cacheDir, fileName);

        // 如果缓存文件不存在，才从 classpath 拷贝
        if (!cachedFile.exists()) {
            ClassPathResource resource = new ClassPathResource(classpathFilePath);
            try (InputStream is = resource.getInputStream()) {
                Files.copy(is, cachedFile.toPath());
            }
        }

        return cachedFile;
    }

    // 新增：按偏移读取指定长度的字节内容
    public static byte[] readBytes(String classpathFilePath, long offset, int length) throws IOException {
        File file = getCachedFile(classpathFilePath);
        try (FileInputStream fis = new FileInputStream(file);
             FileChannel channel = fis.getChannel()) {

            if (offset > channel.size()) {
                throw new IllegalArgumentException("Offset exceeds file size.");
            }

            channel.position(offset*length);
            ByteBuffer buffer = ByteBuffer.allocate(length);

            int bytesRead = channel.read(buffer);
            buffer.flip();

            byte[] result = new byte[buffer.remaining()];
            buffer.get(result);
            return result;
        }
    }

    /**
     * 对二维码取模
     * @param qrcodeBase64 二维码图片的base64
     * @return
     */
    public static String readQrcode(String qrcodeBase64){
        try {
            // 去掉前缀（如果有）
            if (qrcodeBase64.startsWith("data:")) {
                qrcodeBase64 = qrcodeBase64.substring(qrcodeBase64.indexOf(",") + 1);
            }

            // 解码为字节数组
            byte[] imageBytes = Base64.getDecoder().decode(qrcodeBase64);

            // 转换为 BufferedImage
            ByteArrayInputStream bis = new ByteArrayInputStream(imageBytes);
            BufferedImage image = ImageIO.read(bis);

            if (image == null) {
                throw new IllegalArgumentException("无法将 Base64 转换为图像");
            }
            image = convertToGrayScale(image);
            image = convertToBinary(image, 190);
            byte[] bytes = verticalScan(image);
            log.info("二维码base64:" + Base64.getEncoder().encodeToString(bytes));
            return Base64.getEncoder().encodeToString(bytes);
        }catch (Exception e){
            log.info("二维码取模异常", e);
        }
        return "";
    }

    public static BufferedImage generateQRCodeImage(String content, int width, int height) throws Exception {
        Map<EncodeHintType, Object> hints = new HashMap<>();
        hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
        BitMatrix matrix = new MultiFormatWriter().encode(content, BarcodeFormat.QR_CODE, width, height, hints);
        return MatrixToImageWriter.toBufferedImage(matrix);
    }

    // 1. 灰度化
    public static BufferedImage convertToGrayScale(BufferedImage srcImage) {
        int           width     = srcImage.getWidth();
        int           height    = srcImage.getHeight();
        BufferedImage grayImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_GRAY);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb = srcImage.getRGB(x, y);
                int r   = (rgb >> 16) & 0xFF;
                int g   = (rgb >> 8) & 0xFF;
                int b   = rgb & 0xFF;
                // 加权灰度公式（ITU-R BT.601标准）
                int grayValue = (int) (0.299 * r + 0.587 * g + 0.114 * b);
                int grayPixel = (grayValue << 16) | (grayValue << 8) | grayValue;
                grayImage.setRGB(x, y, grayPixel);
            }
        }
        return grayImage;
    }

    // 2. 二值化（需指定阈值）
    public static BufferedImage convertToBinary(BufferedImage grayImage, int threshold) {
        int           width       = grayImage.getWidth();
        int           height      = grayImage.getHeight();
        BufferedImage binaryImage = new BufferedImage(width, height, BufferedImage.TYPE_BYTE_BINARY);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb         = grayImage.getRGB(x, y);
                int grayValue   = (rgb >> 16) & 0xFF; // 提取灰度值
                int binaryValue = (grayValue > threshold) ? 0xFFFFFF : 0x000000; // 白或黑
                binaryImage.setRGB(x, y, binaryValue);
            }
        }
        return binaryImage;
    }

    // 3. 调整亮度（delta范围：-255到255）
    public static BufferedImage adjustBrightness(BufferedImage srcImage, int delta) {
        int           width       = srcImage.getWidth();
        int           height      = srcImage.getHeight();
        BufferedImage brightImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);

        for (int y = 0; y < height; y++) {
            for (int x = 0; x < width; x++) {
                int rgb    = srcImage.getRGB(x, y);
                int r      = Math.max(0, Math.min(255, ((rgb >> 16) & 0xFF) + delta));
                int g      = Math.max(0, Math.min(255, ((rgb >> 8) & 0xFF) + delta));
                int b      = Math.max(0, Math.min(255, (rgb & 0xFF) + delta));
                int newRGB = (r << 16) | (g << 8) | b;
                brightImage.setRGB(x, y, newRGB);
            }
        }
        return brightImage;
    }

    public static int calculateOtsuThreshold(BufferedImage grayImage) {
        int[] histogram   = new int[256];
        int   totalPixels = grayImage.getWidth() * grayImage.getHeight();

        // 统计灰度直方图
        for (int y = 0; y < grayImage.getHeight(); y++) {
            for (int x = 0; x < grayImage.getWidth(); x++) {
                int grayValue = (grayImage.getRGB(x, y) >> 16) & 0xFF;
                histogram[grayValue]++;
            }
        }

        // 计算最大类间方差对应的阈值
        float sum    = 0, sumB = 0;
        int   wB     = 0, wF, threshold = 0;
        float varMax = 0;

        for (int i = 0; i < 256; i++) sum += i * histogram[i];

        for (int t = 0; t < 256; t++) {
            wB += histogram[t];
            if (wB == 0) continue;
            wF = totalPixels - wB;
            if (wF == 0) break;

            sumB += t * histogram[t];
            float mB         = sumB / wB;
            float mF         = (sum - sumB) / wF;
            float varBetween = (float) wB * wF * (mB - mF) * (mB - mF);

            if (varBetween > varMax) {
                varMax = varBetween;
                threshold = t;
            }
        }
        return threshold;
    }

    // 使用伽马校正（Gamma Correction）实现更自然的亮度调节
    public static BufferedImage gammaCorrection(BufferedImage srcImage, float gamma) {
        BufferedImage correctedImage = new BufferedImage(srcImage.getWidth(), srcImage.getHeight(), BufferedImage.TYPE_INT_RGB);
        for (int y = 0; y < srcImage.getHeight(); y++) {
            for (int x = 0; x < srcImage.getWidth(); x++) {
                int rgb    = srcImage.getRGB(x, y);
                int r      = (int) (255 * Math.pow(((rgb >> 16) & 0xFF) / 255.0, gamma));
                int g      = (int) (255 * Math.pow(((rgb >> 8) & 0xFF) / 255.0, gamma));
                int b      = (int) (255 * Math.pow((rgb & 0xFF) / 255.0, gamma));
                int newRGB = (r << 16) | (g << 8) | b;
                correctedImage.setRGB(x, y, newRGB);
            }
        }
        return correctedImage;
    }

    private static byte[] verticalScan(BufferedImage binaryImg) {
        int width = binaryImg.getWidth();
        int height = binaryImg.getHeight();
        int bytesPerColumn = (int) Math.ceil(height / 8.0);
        byte[] result = new byte[width * bytesPerColumn];

        for (int col = 0; col < width; col++) {
            for (int byteNum = 0; byteNum < bytesPerColumn; byteNum++) {
                int byteValue = 0;
                int startRow = byteNum * 8;
                for (int bit = 0; bit < 8; bit++) {
                    int row = startRow + bit;
                    if (row < height) {
//                        double[] pixel = binaryImg.get(row, col);
                        int pixel = binaryImg.getRGB(col, row) & 0xff;
                        if (pixel > 128) { // 白色像素为1
                            byteValue |= (1 << (7 - bit)); // 高位在前
                        }
                    }
                }
                result[col * bytesPerColumn + byteNum] = (byte) byteValue;
            }
        }
        return result;
    }

    /**
     * 根据类型，对文字或二维码地址取模
     * @param msg 文字内容或二维码地址
     * @param type 类型
     * @param size 大小
     * @return
     */
    public static String moduloStrByType(String msg, String type, int size){
        try {
            if ("label".equals(type)) {
                return readFont(msg, size);
            } else if ("qrcode".equals(type)) {
                BufferedImage image = generateQRCodeImage(msg, size, size);
                image = convertToGrayScale(image);
                image = convertToBinary(image, 190);
                byte[] bytes = verticalScan(image);
                log.info("二维码base64:" + Base64.getEncoder().encodeToString(bytes));
                return Base64.getEncoder().encodeToString(bytes);
            }
        }catch (Exception e){
            log.info("取模异常", e);
        }
        return "";
    }
}
