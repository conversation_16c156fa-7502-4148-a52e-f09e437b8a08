package com.byzp.platform.utils;

import javazoom.jl.decoder.*;
import ws.schild.jave.Encoder;
import ws.schild.jave.EncoderException;
import ws.schild.jave.MultimediaObject;
import ws.schild.jave.encode.AudioAttributes;
import ws.schild.jave.encode.EncodingAttributes;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Base64;
import java.util.concurrent.*;

public class AudioConverter {


    public static void getMp3File(){
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "ffmpeg",
                    "-f", "s16le", // 16 位小端序
                    "-ar", "16000",
                    "-ac", "2",
                    "-i", "D:\\voiceTest\\REC.pcm",
                    "-ac", "1",
                    "-ar", "16000",
                    "D:\\voiceTest\\b.mp3"
            );
            Process process = processBuilder.start();
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                System.out.println("PCM 文件已成功转换为 MP3 文件。");
            } else {
                System.err.println("转换过程中出现错误，退出码: " + exitCode);
            }
        } catch (IOException | InterruptedException e) {
            System.err.println("转换过程中出现异常: " + e.getMessage());
        }
    }

    public static String getMp3Base64(){
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(
                    "ffmpeg",
                    "-f", "s16le",
                    "-ar", "16000",
                    "-ac", "2",
                    "-i", "D:\\voiceTest\\REC.pcm",
                    "-ac", "1",
                    "-ar", "16000",
                    "-f", "mp3",
                    "-"
            );
            Process process = processBuilder.start();

            // 获取 FFmpeg 的标准输出流
            InputStream inputStream = process.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 等待 FFmpeg 进程结束
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                // 将音频数据转换为 Base64 编码
                byte[] audioData = outputStream.toByteArray();
                String base64Audio = Base64.getEncoder().encodeToString(audioData);
                System.out.println("转换后的 MP3 音频 Base64 数据: " + base64Audio);
            } else {
                System.err.println("转换过程中出现错误，退出码: " + exitCode);
            }
        } catch (IOException | InterruptedException e) {
            System.err.println("转换过程中出现异常: " + e.getMessage());
        }
        return null;
    }

    /**
     * 把双声道的pcm输入流转成单声道的mp3，返回base64
     * @param voiceBytes
     * @return
     */
    public static String getMp3Base64FromByte(byte[] voiceBytes){
        try {
            InputStream pcmInputStream = new ByteArrayInputStream(voiceBytes);

            ProcessBuilder processBuilder = new ProcessBuilder(
                    "ffmpeg",
                    "-f", "s16le",
                    "-ar", "16000",
                    "-ac", "2",
                    "-i", "-", // 从标准输入读取数据
                    "-ac", "1",
                    "-ar", "16000",
                    "-f", "mp3",
                    "-"
            );
            Process process = processBuilder.start();

            // 将 PCM 输入流的数据写入 FFmpeg 进程的标准输入
            OutputStream ffmpegInput = process.getOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = pcmInputStream.read(buffer)) != -1) {
                ffmpegInput.write(buffer, 0, bytesRead);
            }
            ffmpegInput.close();

            // 获取 FFmpeg 的标准输出流
            InputStream ffmpegOutput = process.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            while ((bytesRead = ffmpegOutput.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 等待 FFmpeg 进程结束
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                // 将音频数据转换为 Base64 编码
                byte[] audioData = outputStream.toByteArray();
                String base64Audio = Base64.getEncoder().encodeToString(audioData);
//                System.out.println("转换后的 MP3 音频 Base64 数据: " + base64Audio);
                return base64Audio;
            } else {
                System.err.println("转换过程中出现错误，退出码: " + exitCode);
            }

            // 关闭输入流
            pcmInputStream.close();
        } catch (IOException | InterruptedException e) {
            System.err.println("转换过程中出现异常: " + e.getMessage());
        }
        return null;
    }

    public static void getMp3FileFromByte(){
        try {
            // 这里模拟一个输入流，实际使用时替换为真实的输入流
            InputStream pcmInputStream = new FileInputStream("D:\\voiceTest\\REC.pcm");

            ProcessBuilder processBuilder = new ProcessBuilder(
                    "ffmpeg",
                    "-f", "s16le",
                    "-ar", "16000",
                    "-ac", "2",
                    "-i", "-", // 从标准输入读取数据
                    "-ac", "1",
                    "-ar", "16000",
                    "D:\\voiceTest\\bb.mp3"
            );
            Process process = processBuilder.start();

            // 将 PCM 输入流的数据写入 FFmpeg 进程的标准输入
            OutputStream ffmpegInput = process.getOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = pcmInputStream.read(buffer)) != -1) {
                ffmpegInput.write(buffer, 0, bytesRead);
            }
            ffmpegInput.close();

            // 等待 FFmpeg 进程结束
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                System.out.println("PCM 数据已成功转换为 b.mp3 文件。");
            } else {
                System.err.println("转换过程中出现错误，退出码: " + exitCode);
            }

            // 关闭输入流
            pcmInputStream.close();
        } catch (IOException | InterruptedException e) {
            System.err.println("转换过程中出现异常: " + e.getMessage());
        }
    }

    public static void getPcmFileFromByte(){
        try {
            // 这里模拟一个输入流，实际使用时替换为真实的输入流
            InputStream pcmInputStream = new FileInputStream("D:\\voiceTest\\REC.pcm");

            ProcessBuilder processBuilder = new ProcessBuilder(
                    "ffmpeg",
                    "-f", "s16le",
                    "-ar", "16000",
                    "-ac", "2",
                    "-i", "-", // 从标准输入读取数据
                    "-f", "s16le",
                    "-ac", "1",
                    "-ar", "16000",
                    "D:\\voiceTest\\RECAR1.pcm"
            );
            Process process = processBuilder.start();

            // 将 PCM 输入流的数据写入 FFmpeg 进程的标准输入
            OutputStream ffmpegInput = process.getOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = pcmInputStream.read(buffer)) != -1) {
                ffmpegInput.write(buffer, 0, bytesRead);
            }
            ffmpegInput.close();

            // 等待 FFmpeg 进程结束
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                System.out.println("PCM 数据已成功转换为 RECAR1.pcm 文件。");
            } else {
                System.err.println("转换过程中出现错误，退出码: " + exitCode);
            }

            // 关闭输入流
            pcmInputStream.close();
        } catch (IOException | InterruptedException e) {
            System.err.println("转换过程中出现异常: " + e.getMessage());
        }
    }

    /**
     * 把双声道的pcm输入流转成单声道的pcm，返回byte[]
     * @param voiceBytes
     * @return
     */
    public static byte[] getPcmBase64FromPcmByte(byte[] voiceBytes){
        try {
            // 这里模拟一个输入流，实际使用时替换为真实的输入流
            InputStream pcmInputStream = new ByteArrayInputStream(voiceBytes);

            ProcessBuilder processBuilder = new ProcessBuilder(
                    "ffmpeg",
                    "-f", "s16le",
                    "-ar", "16000",
                    "-ac", "2",
                    "-i", "-", // 从标准输入读取数据
                    "-f", "s16le",
                    "-ac", "1",
                    "-ar", "16000",
                    "-"
            );
            Process process = processBuilder.start();

            // 将 PCM 输入流的数据写入 FFmpeg 进程的标准输入
            OutputStream ffmpegInput = process.getOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = pcmInputStream.read(buffer)) != -1) {
                ffmpegInput.write(buffer, 0, bytesRead);
            }
            ffmpegInput.close();

            // 获取 FFmpeg 的标准输出流
            InputStream ffmpegOutput = process.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            while ((bytesRead = ffmpegOutput.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 等待 FFmpeg 进程结束
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                // 将音频数据转换为 Base64 编码
                byte[] audioData = outputStream.toByteArray();
//                String base64Audio = Base64.getEncoder().encodeToString(audioData);
//                System.out.println("转换后的 MP3 音频 Base64 数据: " + base64Audio);
                return audioData;
            } else {
                System.err.println("转换过程中出现错误，退出码: " + exitCode);
            }

            // 关闭输入流
            pcmInputStream.close();
        } catch (IOException | InterruptedException e) {
            System.err.println("转换过程中出现异常: " + e.getMessage());
        }
        return null;
    }

    /**
     * 把单声道的mp3输入流转成单声道的pcm，返回byte[]
     * @param
     * @return
     */
    public static byte[] getPcmBase64FromMp3Byte(byte[] mp3Bytes) throws Throwable{
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-f", "mp3",
                "-i", "-",
                "-f", "s16le",
                "-ac", "1",
                "-ar", "16000",
                "-"
        );

        Process process = processBuilder.start();

        OutputStream ffmpegInput = process.getOutputStream();
        InputStream ffmpegOutput = process.getInputStream();
        InputStream errorStream = process.getErrorStream();

        // 分块写入数据
        int chunkSize = 4096;
        ExecutorService executor = Executors.newSingleThreadExecutor();
        Future<?> writeFuture = executor.submit(() -> {
            try {
                for (int i = 0; i < mp3Bytes.length; i += chunkSize) {
                    int length = Math.min(chunkSize, mp3Bytes.length - i);
                    ffmpegInput.write(mp3Bytes, i, length);
                    ffmpegInput.flush();
                }
                ffmpegInput.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        });

        try {
            // 设置写入操作的超时时间为 10 秒，可根据实际情况调整
            writeFuture.get(10, TimeUnit.SECONDS);
        } catch (ExecutionException e) {
            throw new IOException("写入数据时出现异常", e.getCause());
        } catch (TimeoutException e) {
            // 超时处理，终止 FFmpeg 进程
            process.destroy();
            throw e;
        } finally {
            executor.shutdown();
        }

        // 启动线程读取标准输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        Thread outputThread = new Thread(() -> {
            try {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = ffmpegOutput.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
        outputThread.start();

        // 启动线程读取标准错误流
        ByteArrayOutputStream errorOutput = new ByteArrayOutputStream();
        Thread errorThread = new Thread(() -> {
            try {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = errorStream.read(buffer)) != -1) {
                    errorOutput.write(buffer, 0, bytesRead);
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
        errorThread.start();

        // 等待输出线程和错误线程结束
        outputThread.join();
        errorThread.join();

        // 打印错误信息
        System.out.println("FFmpeg 错误信息: " + errorOutput.toString());

        // 等待 FFmpeg 进程执行完成
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("FFmpeg 进程执行失败，退出码: " + exitCode);
        }

        return outputStream.toByteArray();
    }

    public static byte[] convertMp3ToPcm(String base64Mp3) throws IOException, InterruptedException, BitstreamException, DecoderException {
        // 1. Base64解码获取MP3字节
        byte[] mp3Bytes = Base64.getDecoder().decode(base64Mp3);
        ByteArrayInputStream mp3Stream = new ByteArrayInputStream(mp3Bytes);
        Bitstream bitstream = new Bitstream(mp3Stream);
        Decoder decoder = new Decoder(); // 确保Decoder初始化无误
        ByteArrayOutputStream pcmStream = new ByteArrayOutputStream();

        try {
            Header frameHeader;
            // 2. 逐帧解码MP3
            while ((frameHeader = bitstream.readFrame()) != null) {
                SampleBuffer sampleBuffer = (SampleBuffer) decoder.decodeFrame(frameHeader, bitstream);
                short[] pcmShorts = sampleBuffer.getBuffer(); // 获取16位PCM的short数组
                System.out.println("解码帧: 采样数=" + pcmShorts.length + ", 声道数=" + sampleBuffer.getChannelCount());
                // 3. 将short数组转为小端字节序的byte数组
                byte[] pcmBytes = new byte[pcmShorts.length * 2];
                for (int i = 0; i < pcmShorts.length; i++) {
                    pcmBytes[2*i] = (byte) (pcmShorts[i] & 0xFF);       // 低字节
                    pcmBytes[2*i + 1] = (byte) ((pcmShorts[i] >> 8) & 0xFF); // 高字节
                }
                pcmStream.write(pcmBytes);
                bitstream.closeFrame();
            }
        } finally {
            bitstream.close();
        }

        // 4. 返回最终的PCM字节数组
        return pcmStream.toByteArray();
    }


    public static void convertPcmBase64ToMp3File(String pcmBase64, String mp3FilePath) throws IOException, InterruptedException {
        // 对 PCM 的 Base64 编码进行解码
        byte[] pcmBytes = Base64.getDecoder().decode(pcmBase64);

        // 构建 FFmpeg 命令
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-f", "s16le",
                "-ac", "1",
                "-ar", "16000",
                "-i", "-",
                "-ac","1",
                "-b:a","128k",
                mp3FilePath
        );

        // 启动 FFmpeg 进程
        Process process = processBuilder.start();

        // 获取 FFmpeg 进程的输入流
        OutputStream ffmpegInput = process.getOutputStream();
        ffmpegInput.write(pcmBytes);
        ffmpegInput.close();

        // 获取 FFmpeg 进程的错误流
        InputStream errorStream = process.getErrorStream();
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(errorStream));
        String line;
        while ((line = errorReader.readLine()) != null) {
            System.err.println(line);
        }

        // 等待 FFmpeg 进程完成
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("FFmpeg 进程执行失败，退出码: " + exitCode);
        }
    }

    public static void convertPcmBase64ToMp3File2(String pcmBase64, String mp3FilePath) throws IOException, InterruptedException {
        // 对 PCM 的 Base64 编码进行解码
        byte[] pcmBytes = Base64.getDecoder().decode(pcmBase64);

        // 构建 FFmpeg 命令
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-f", "s16le",
                "-ac", "2",
                "-ar", "16000",
                "-i", "-",
                "-ac","1",
                mp3FilePath
        );

        // 启动 FFmpeg 进程
        Process process = processBuilder.start();

        // 获取 FFmpeg 进程的输入流
        OutputStream ffmpegInput = process.getOutputStream();
        ffmpegInput.write(pcmBytes);
        ffmpegInput.close();

        // 获取 FFmpeg 进程的错误流
        InputStream errorStream = process.getErrorStream();
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(errorStream));
        String line;
        while ((line = errorReader.readLine()) != null) {
            System.err.println(line);
        }

        // 等待 FFmpeg 进程完成
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("FFmpeg 进程执行失败，退出码: " + exitCode);
        }
    }

    /**
     * 这个方法没问题
     * @param filePath
     * @return
     */
    public static String audioFileToBase64(String filePath) {
        File file = new File(filePath);
        try (FileInputStream fileInputStream = new FileInputStream(file)) {
            byte[] bytes = new byte[(int) file.length()];
            fileInputStream.read(bytes);
            return Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 这个方法没问题
     * @param base64Encoded
     * @return
     */
    public static byte[] decodeBase64ToByteArray(String base64Encoded){
        Base64.Decoder decoder = Base64.getDecoder();
        return decoder.decode(base64Encoded);
    }

    public static void convertMp3Base64ToPcmFile(String mp3Base64, String pcmFilePath) throws IOException, InterruptedException {
        // 解码 Base64 数据
        byte[] mp3Bytes = Base64.getDecoder().decode(mp3Base64);

        // 构建 FFmpeg 命令
        ProcessBuilder processBuilder = new ProcessBuilder(
                "ffmpeg",
                "-f", "mp3",
                "-i", "-",
                "-f", "s16le",
                "-ac", "1",
                "-ar", "16000",
                pcmFilePath
        );

        // 启动 FFmpeg 进程
        Process process = processBuilder.start();

        // 获取 FFmpeg 进程的输入流
        OutputStream ffmpegInput = process.getOutputStream();
        ffmpegInput.write(mp3Bytes);
        ffmpegInput.close();

        // 获取 FFmpeg 进程的错误流
        InputStream errorStream = process.getErrorStream();
        BufferedReader errorReader = new BufferedReader(new InputStreamReader(errorStream));
        String line;
        while ((line = errorReader.readLine()) != null) {
            System.err.println(line);
        }

        // 等待 FFmpeg 进程完成
        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("FFmpeg 进程执行失败，退出码: " + exitCode);
        }
    }
}
