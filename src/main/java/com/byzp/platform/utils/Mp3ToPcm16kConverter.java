package com.byzp.platform.utils;


import javazoom.jl.decoder.Bitstream;
import javazoom.jl.decoder.Decoder;
import javazoom.jl.decoder.Header;
import javazoom.jl.decoder.SampleBuffer;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileInputStream;
import java.util.Arrays;
import java.util.Base64;

public class Mp3ToPcm16kConverter {

    /**
     * 统一入口：支持Base64字符串或byte[]输入
     */
    public static String convertMP3ToPCMBase64(Object mp3Input, double maxDurationSeconds) throws Exception {
        byte[] mp3Bytes;

        if (mp3Input instanceof String) {
            // Base64字符串转byte[]
            mp3Bytes = Base64.getDecoder().decode((String) mp3Input);
        } else if (mp3Input instanceof byte[]) {
            // 直接使用byte[]
            mp3Bytes = (byte[]) mp3Input;
        } else if (mp3Input instanceof FileInputStream){
            mp3Bytes = ((FileInputStream) mp3Input).readAllBytes();
        }else {
            throw new IllegalArgumentException("输入类型必须是Base64字符串或byte[]");
        }

        // 解码并返回Base64
        byte[] pcmData = decodeMP3(mp3Bytes, maxDurationSeconds);
        return Base64.getEncoder().encodeToString(pcmData);
    }

    /**
     * 核心解码逻辑
     */
    private static byte[] decodeMP3(byte[] mp3Bytes, double maxDurationSeconds) throws Exception {
        ByteArrayOutputStream pcmStream = new ByteArrayOutputStream();
        try (ByteArrayInputStream bis = new ByteArrayInputStream(mp3Bytes)) {
            Bitstream bitstream = new Bitstream(bis);
            Decoder decoder = new Decoder();
            Header frameHeader;
            double totalDuration = 0.0;
            double totalDurationMs = 0; // 总时长（毫秒）
            while ((frameHeader = bitstream.readFrame()) != null) {
                SampleBuffer sampleBuffer = (SampleBuffer) decoder.decodeFrame(frameHeader, bitstream);
                // 检查当前帧时长是否超限
                double frameMs = frameHeader.ms_per_frame();
                if (totalDurationMs + frameMs > maxDurationSeconds * 1000) {
                    break; // 超过30秒则终止
                }
                totalDurationMs += frameMs;
                // 1. 强制校验参数
                if (sampleBuffer.getSampleFrequency() != 16000) {
                    throw new IllegalArgumentException("MP3采样率必须为16kHz");
                }
                if (sampleBuffer.getChannelCount() != 1) {
                    throw new IllegalArgumentException("MP3必须为单声道");
                }

                // 2. 计算当前帧时长
                short[] pcmSamples = sampleBuffer.getBuffer();
                int samplesPerFrame = pcmSamples.length;
                double frameDuration = (double) samplesPerFrame / 16000.0; // 16kHz采样率
                double remainingTime = maxDurationSeconds - totalDuration;

                if (remainingTime <= 0) break;

                short[] samples = sampleBuffer.getBuffer();
                byte[] pcmBytes;

                if (frameDuration <= remainingTime) {
                    // 整帧写入
                    pcmBytes = convertToLittleEndianBytes(samples);
                    totalDuration += frameDuration;
                } else {
                    // 3. 截断到剩余时间对应的样本数
                    int maxSamples = (int) (remainingTime * 16000);
                    int actualSamples = Math.min(maxSamples, pcmSamples.length); // 防止越界‌:ml-citation{ref="6" data="citationList"}
                    short[] truncated = Arrays.copyOfRange(pcmSamples, 0, actualSamples);
                    pcmBytes = convertToLittleEndianBytes(truncated);
                    totalDuration = maxDurationSeconds; // 强制结束
                }

                pcmStream.write(pcmBytes);
                bitstream.closeFrame();

                if (totalDuration >= maxDurationSeconds) break;
            }
        }
        return pcmStream.toByteArray();
    }

    /**
     * 将short[]转为小端序byte[]
     */
    private static byte[] convertToLittleEndianBytes(short[] samples) {
        byte[] bytes = new byte[samples.length * 2];
        for (int i = 0; i < samples.length; i++) {
            bytes[i * 2] = (byte) (samples[i] & 0xFF);      // 低字节
            bytes[i * 2 + 1] = (byte) ((samples[i] >> 8) & 0xFF); // 高字节
        }
        return bytes;
    }
}
