//package com.byzp.platform.utils;
//
//import javazoom.jl.decoder.Bitstream;
//import javazoom.jl.decoder.Decoder;
//import javazoom.jl.decoder.Header;
//import javazoom.jl.decoder.SampleBuffer;
//import java.io.*;
//import java.nio.file.Files;
//import java.util.Base64;
//import java.util.ArrayList;
//import java.util.List;
//
//public class Mp3Base64ToPcmBase64 {
//
//    public static String convert(String base64Mp3) throws Exception {
//        // 1. Base64解码MP3
//        byte[] mp3Bytes = Base64.getDecoder().decode(base64Mp3);
//
//        // 2. 解码MP3为原始PCM样本
//        DecodedPcm pcm = decodeMp3ToRawPcm(mp3Bytes);
//
//        // 3. 转换到目标格式（16kHz、单声道）
//        List<Short> processedSamples = processPcm(pcm.samples, pcm.sampleRate, pcm.channels);
//
//        // 4. 转换为小端字节序的byte[]并Base64编码
//        byte[] pcmBytes = convertShortsToBytes(processedSamples);
//        return Base64.getEncoder().encodeToString(pcmBytes);
//    }
//
//    // 解码MP3到原始PCM样本（包含采样率、声道信息）
//    private static DecodedPcm decodeMp3ToRawPcm(byte[] mp3Bytes) throws Exception {
//        List<Short> samples = new ArrayList<>();
//        Bitstream bitstream = new Bitstream(new ByteArrayInputStream(mp3Bytes));
//        Decoder decoder = new Decoder();
//        int sampleRate = 0;
//        int channels = 0;
//
//        try {
//            Header frameHeader;
//            boolean firstFrame = true;
//            while ((frameHeader = bitstream.readFrame()) != null) {
//                SampleBuffer buffer = (SampleBuffer) decoder.decodeFrame(frameHeader, bitstream);
//
//                // 记录第一帧的采样率和声道数
//                if (firstFrame) {
//                    sampleRate = getSampleRate(frameHeader);
//                    channels = buffer.getChannelCount();
//                    firstFrame = false;
//                }
//
//                // 收集原始样本
//                short[] frameSamples = buffer.getBuffer();
//                for (short s : frameSamples) {
//                    samples.add(s);
//                }
//                bitstream.closeFrame();
//            }
//        } finally {
//            bitstream.close();
//        }
//        return new DecodedPcm(samples, sampleRate, channels);
//    }
//
//    // 处理PCM（重采样 + 转单声道）
//    private static List<Short> processPcm(List<Short> samples, int originalRate, int originalChannels) {
//        // 转单声道
//        List<Short> mono = convertToMono(samples, originalChannels);
//        // 重采样到16kHz
//        return resample(mono, originalRate, 16000);
//    }
//
//    // 立体声转单声道
//    private static List<Short> convertToMono(List<Short> samples, int channels) {
//        if (channels == 1) return samples;
//        List<Short> mono = new ArrayList<>();
//        for (int i = 0; i < samples.size(); i += channels) {
//            long sum = 0;
//            for (int j = 0; j < channels; j++) {
//                sum += samples.get(i + j);
//            }
//            mono.add((short) (sum / channels));
//        }
//        return mono;
//    }
//
//    // 重采样（线性插值）
//    private static List<Short> resample(List<Short> samples, int originalRate, int targetRate) {
//        if (originalRate == targetRate) return samples;
//        double ratio = (double) originalRate / targetRate;
//        List<Short> resampled = new ArrayList<>();
//        for (double i = 0; i < samples.size(); i += ratio) {
//            int index = (int) i;
//            if (index >= samples.size() - 1) break;
//            double fraction = i - index;
//            short prev = samples.get(index);
//            short next = samples.get(index + 1);
//            resampled.add((short) (prev + (next - prev) * fraction));
//        }
//        return resampled;
//    }
//
//    // short数组转小端字节序byte[]
//    private static byte[] convertShortsToBytes(List<Short> samples) {
//        byte[] bytes = new byte[samples.size() * 2];
//        for (int i = 0; i < samples.size(); i++) {
//            short s = samples.get(i);
//            bytes[2 * i] = (byte) (s & 0xFF);      // 低字节
//            bytes[2 * i + 1] = (byte) (s >> 8);    // 高字节
//        }
//        return bytes;
//    }
//
//    // 辅助类：存储解码后的PCM信息
//    private static class DecodedPcm {
//        List<Short> samples;
//        int sampleRate;
//        int channels;
//
//        DecodedPcm(List<Short> samples, int sampleRate, int channels) {
//            this.samples = samples;
//            this.sampleRate = sampleRate;
//            this.channels = channels;
//        }
//    }
//
//    // 获取实际采样率
//    private static int getSampleRate(Header header) {
//        int[] rates = {44100, 48000, 32000};
//        return rates[header.getSampleRate()];
//    }
//
//    // 测试
//    public static void main(String[] args) {
//        String base64Mp3 = "填入你的Base64字符串";
//        try {
//            String pcmBase64 = convert(base64Mp3);
//            System.out.println("PCM Base64结果:\n" + pcmBase64);
//
//            // 可选：保存为PCM文件验证
//            byte[] pcmBytes = Base64.getDecoder().decode(pcmBase64);
//            Files.write(new File("output.pcm").toPath(), pcmBytes);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//}