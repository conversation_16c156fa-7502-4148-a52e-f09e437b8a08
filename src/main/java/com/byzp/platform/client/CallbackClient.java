package com.byzp.platform.client;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.JSONBody;
import com.dtflys.forest.annotation.Request;
import com.dtflys.forest.annotation.Var;

public interface CallbackClient {

    /**
     * 回调应用地址
     * @return
     */
    @Request(
            type = "post",
            headers = {
                    "Content-Type: application/json"
            },
            url = "{apiUrl}"
    )
    JSONObject callback(@Var("apiUrl") String apiUrl, @JSONBody JSONObject param);

    @Request(
            type = "post",
            headers = {
                    "Content-Type: application/json",
                    "msgSource: {msgSource}"
            },
            url = "{apiUrl}"
    )
    String callback(@Var("apiUrl") String apiUrl, @Body String body, @Var("msgSource") String msgSource);
}
