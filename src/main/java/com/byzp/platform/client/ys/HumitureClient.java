package com.byzp.platform.client.ys;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Request;
import com.dtflys.forest.annotation.Var;

public interface HumitureClient {

    /**
     * 查询剩余电量
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/PowerMgr/SurplusPower"
    )
    JSONObject querySurplusPower(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置剩余电量
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/PowerMgr/SurplusPower"
    )
    JSONObject setSurplusPower(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body Integer data);


    /**
     * 查询湿度
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/Humiture/Humidity"
    )
    JSONObject queryHumidity(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置湿度
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/Humiture/Humidity"
    )
    JSONObject setHumidity(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body Integer data);


    /**
     * 查询温度
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/Humiture/Temperature"
    )
    JSONObject queryTemperature(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置温度
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/Humiture/Temperature"
    )
    JSONObject setTemperature(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body double data);


    /**
     * 查询测温单位
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/Humiture/TemperatureUnit"
    )
    JSONObject queryTemperatureUnit(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置测温单位
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/Humiture/TemperatureUnit"
    )
    JSONObject setTemperatureUnit(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body("unit") String unit);


    /**
     * 查询湿度告警阈值
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/Humiture/HumidityAlarmThreshold"
    )
    JSONObject queryHumidityAlarmThreshold(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置湿度告警阈值
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/Humiture/HumidityAlarmThreshold"
    )
    JSONObject setHumidityAlarmThreshold(@Var("accessToken") String accessToken,
                                         @Var("deviceSerial") String deviceSerial,
                                         @Body("maxHumidity") int maxHumidity,
                                         @Body("minHumidity") int minHumidity,
                                         @Body("enabled") boolean enabled);

    /**
     * 查询温度告警阈值
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/Humiture/TemperatureAlarmThreshold"
    )
    JSONObject queryTemperatureAlarmThreshold(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置温度告警阈值
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/Humiture/TemperatureAlarmThreshold"
    )
    JSONObject setTemperatureAlarmThreshold(@Var("accessToken") String accessToken,
                                            @Var("deviceSerial") String deviceSerial,
                                            @Body("minTemperature") double minTemperature,
                                            @Body("maxTemperature") double maxTemperature,
                                            @Body("enabled") boolean enabled,
                                            @Body("unit") String unit);


    /**
     * 查询检测模式
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/TempHumiSensorMgr/DetectionMode"
    )
    JSONObject queryDetectionMode(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置检测模式
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/TempHumiSensorMgr/DetectionMode"
    )
    JSONObject setDetectionMode(@Var("accessToken") String accessToken,
                                            @Var("deviceSerial") String deviceSerial,
                                            @Body("mode") String mode,
                                            @Body("dataType") String dataType);

    /**
     * 获取所有温湿度信息
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/action/{deviceSerial}/global/0/TempHumiSensorMgr/GetTempHumiInfoList"
    )
    JSONObject queryTempHumiInfoList(@Var("accessToken") String accessToken,
                                   @Var("deviceSerial") String deviceSerial);


    /**
     * 获取单个温湿度信息
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/action/{deviceSerial}/global/0/TempHumiSensorMgr/GetTempHumiInfo"
    )
    JSONObject queryTempHumiInfo(@Var("accessToken") String accessToken,
                                 @Var("deviceSerial") String deviceSerial,
                                 @Body("ID") Integer id);
}
