package com.byzp.platform.client.ys;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.dto.ys.YsResult;
import com.dtflys.forest.annotation.Request;
import com.dtflys.forest.annotation.Var;

public interface YsClient {

    @Request(
            type = "post",
            url = "#{ys.apiUrl}/lapp/token/get?appKey=#{ys.appKey}&appSecret=#{ys.secret}"
    )
    JSONObject getToken();

    @Request(
            type = "post",
            url = "#{ys.apiUrl}/lapp/device/add?deviceSerial={deviceSerial}&validateCode={validateCode}&accessToken={accessToken}"
    )
    YsResult addDevice(@Var("accessToken") String accessToken,
                       @Var("deviceSerial") String deviceSerial,
                       @Var("validateCode") String validateCode);

    /**
     * 自动确权
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            url = "#{ys.apiUrl}/userdevice/v3/devices/op/permission?accessToken={accessToken}&deviceSerial={deviceSerial}"
    )
    JSONObject autoPermission(@Var("accessToken") String accessToken,
                              @Var("deviceSerial") String deviceSerial);

    /**
     * 返回设备在线状态(离线确认)
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            url = "#{ys.apiUrl}/userdevice/v3/devices/realtimestatus?accessToken={accessToken}&deviceSerial={deviceSerial}"
    )
    JSONObject offlineConfirm(@Var("accessToken") String accessToken,
                              @Var("deviceSerial") String deviceSerial);


    /**
     * 在线确权
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            url = "#{ys.apiUrl}/userdevice/v3/devices/permission?accessToken={accessToken}&deviceSerial={deviceSerial}"
    )
    JSONObject doPermission(@Var("accessToken") String accessToken,
                              @Var("deviceSerial") String deviceSerial);

    /**e
     * 在线确权
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "post",
            url = "#{ys.apiUrl}/lapp/device/info?accessToken={accessToken}&deviceSerial={deviceSerial}"
    )
    JSONObject singleDeviceInfo(@Var("accessToken") String accessToken,
                            @Var("deviceSerial") String deviceSerial);


    /**
     * 设备基础信息查询
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            url = "#{ys.apiUrl}/v3/device/searchDeviceInfo?accessToken={accessToken}&deviceSerial={deviceSerial}"
    )
    JSONObject baseDeviceInfo(@Var("accessToken") String accessToken,
                              @Var("deviceSerial") String deviceSerial);


    @Request(
            type = "post",
            url = "#{ys.apiUrl}/lapp/device/delete?deviceSerial={deviceSerial}&accessToken={accessToken}"
    )
    JSONObject deleteDevice(@Var("accessToken") String accessToken,
                       @Var("deviceSerial") String deviceSerial);
}
