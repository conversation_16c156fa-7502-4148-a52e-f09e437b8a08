package com.byzp.platform.client.ys;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.dto.ys.ChildDeviceParam;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Request;
import com.dtflys.forest.annotation.Var;

import java.util.List;

public interface DeviceGatewayClient {

    /**
     * 一键消警
     *
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(type = "post", url = "#{ys.apiUrl}/lapp/detector/cancelAlarm?accessToken={accessToken}&deviceSerial={deviceSerial}")
    JSONObject cancelAlarm(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 关联子设备
     *
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(type = "post", headers = {"Content-Type: application/json", "accessToken: {accessToken}", "deviceSerial: {deviceSerial}",}, url = "#{ys.apiUrl}/route/userdevicetob/v3/devices/childDevice/link")
    JSONObject linkChildDevices(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body("deviceSerial") String deviceSerialBody, @Body("childDevices") List<ChildDeviceParam> list);

    /**
     * 删除子设备
     *
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(type = "post", headers = {"Content-Type: application/json", "accessToken: {accessToken}", "deviceSerial: {deviceSerial}",}, url = "#{ys.apiUrl}/route/userdevicetob/v3/devices/childDevice/unlink")
    JSONObject unlinkChildDevices(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body("childDevices") List<ChildDeviceParam> list);


    /**
     * 网关下子设备列表
     *
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(type = "get", headers = {"Content-Type: application/json", "accessToken: {accessToken}", "deviceSerial: {deviceSerial}",}, url = "#{ys.apiUrl}/route/userdevicetob/v3/devices/childDevice/list")
    JSONObject childDeviceList(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);
}
