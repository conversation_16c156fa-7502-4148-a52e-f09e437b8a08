package com.byzp.platform.client.ys;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Request;
import com.dtflys.forest.annotation.Var;

/**
 * T1C人体移动传感器
 */
@BaseRequest(
        baseURL = "#{ys.apiUrl}",
        headers = {
                "Content-Type: application/json",
                "accessToken: {accessToken}"
        }
)
public interface T1CClient {

    /**
     * 查询pir状态
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/HumanSensor/PirStatus"
    )
    JSONObject selectPirStatus(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置pir状态
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  pir状态 1-有人移动 2-正常
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/HumanSensor/PirStatus"
    )
    JSONObject setPirStatus(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Integer data);


    /**
     * 查询提醒开关
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/HumanSensor/RemindSwitch"
    )
    JSONObject selectRemindSwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置提醒开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  提醒开关
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/HumanSensor/RemindSwitch"
    )
    JSONObject setRemindSwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Boolean data);


    /**
     * 查询运行模式
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/HumanSensor/RunningMode"
    )
    JSONObject selectRunningMode(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置运行模式
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  设备模式	  EnergySaving-节能模式  Smart-智能家居模式
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/HumanSensor/RunningMode"
    )
    JSONObject setRunningMode(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);


    /**
     * 一键消警
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/action/{deviceSerial}/global/0/HumanSensor/Erasure"
    )
    JSONObject erasure(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 查询提示音音量
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/PromptToneVolume"
    )
    JSONObject selectPromptToneVolume(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置提示音音量
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  提示音音量
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/PromptToneVolume"
    )
    JSONObject setPromptToneVolume(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Integer data);


    /**
     * 查询提醒使能开关
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/RemindEnabled"
    )
    JSONObject selectRemindEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置提醒使能开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  提醒使能开关
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/RemindEnabled"
    )
    JSONObject setRemindEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Boolean data);

    /**
     * 查询提示音类型
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/SoundTypes"
    )
    JSONObject selectSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置提示音类型
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  提示音类型 0-告警音 1-自定义语音
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/SoundTypes"
    )
    JSONObject setSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);


    /**
     * 查询自定义声音
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/CustomSound"
    )
    JSONObject selectCustomSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置自定义声音
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  音效 1-叮咚  2-有人闯入  3-欢迎光临  4-请随时关门  5-请注意安全
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/CustomSound"
    )
    JSONObject setCustomSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);



    /**
     * 查询告警声音使能开关
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/AlarmSoundEnabled"
    )
    JSONObject selectAlarmSoundEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置告警声音使能开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  voiceIndex（integer） 自定义语音的索引  enabled（boolean）  声音开关  soundType（integer） 声音类型 0-短叫 1-长叫 2-静音 3-自定义语音
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/AlarmSoundEnabled"
    )
    JSONObject setAlarmSoundEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);



    /**
     * 播放指定声音
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  volume（integer） 音量  playSoundType（string）  声音类型 alarm-告警音  call-呼叫音    index（integer） 声音索引
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/action/{deviceSerial}/global/0/SoundSetting/PlaySpecificSound"
    )
    JSONObject setPlaySpecificSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);


    /**
     * 查询网关自定义声音
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/CustomSound"
    )
    JSONObject selectGatewayRemindCustomSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置网关自定义声音
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  音效 1-叮咚 2-有人闯入 3-欢迎光临 4-请随时关门 5-请注意安全 20-微风 21-警告 22-叮 23-回声 24-退场 25-前进 26-激光 27-钢琴键 28-前奏 29-渐进 30-脉冲 31-叮咛 32-上课铃 33-激板 34-柔和 35-舒缓 36-滴答 37-按键音 38-振奋 39-圆舞曲
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/CustomSound"
    )
    JSONObject setGatewayRemindCustomSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);



    /**
     * 查询网关提醒开关
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/GateWayRemindSwitch"
    )
    JSONObject selectGateWayRemindSwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);



    /**
     * 设置网关提醒开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  网关提醒开关
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/GateWayRemindSwitch"
    )
    JSONObject setGateWayRemindSwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Boolean data);


    /**
     * 查询网关提示音类型
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/SoundTypes"
    )
    JSONObject selectGatewayRemindSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);



    /**
     * 设置网关提示音类型
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  提示音类型 0-告警音 1-自定义语音
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/SoundTypes"
    )
    JSONObject setGatewayRemindSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);


    /**
     * 查询网关声音提醒状态
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/SoundRemindStatus"
    )
    JSONObject selectSoundRemindStatus(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);



    /**
     * 设置网关声音提醒状态
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  网关声音提醒状态
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/SoundRemindStatus"
    )
    JSONObject setSoundRemindStatus(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Boolean data);


    /**
     * 查询剩余电量
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/BatteryInfo/SurplusPower"
    )
    JSONObject selectSurplusPower(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);



    /**
     * 设置剩余电量
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  剩余电量
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/BatteryInfo/SurplusPower"
    )
    JSONObject setSurplusPower(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Integer data);


}
