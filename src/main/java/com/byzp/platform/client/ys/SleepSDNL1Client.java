package com.byzp.platform.client.ys;


import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.*;

/**
 * 睡眠伴侣CS-EP-SDNL1
 */
@BaseRequest(
        baseURL = "#{ys.apiUrl}"
)
public interface SleepSDNL1Client {


    /**
     * 睡眠伴侣的统一接口 根据传参 不同功能
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "{type}",
            url = "/v3/device/otap/prop",
            headers = {
            "Content-Type:application/json",
            "accessToken:{accessToken}",
            "deviceSerial:{deviceSerial}",
            "localIndex:{localIndex}",
            "resourceCategory:{resourceCategory}",
            "domainIdentifier:{domainIdentifier}",
            "propIdentifier:{propIdentifier}"
            }
    )
    JSONObject prop(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial
    ,@Var("localIndex") Integer localIndex
    ,@Var("resourceCategory") String resourceCategory
    ,@Var("domainIdentifier") String domainIdentifier
    ,@Var("propIdentifier") String propIdentifier
    ,@Var("type") String type, @Body String body
    );


    /**
     * 森思泰克睡眠仪-获取睡眠报告
     * @param accessToken
     * @param startDate 开始日期，格式：yyyy-MM-dd
     * @param endDate 结束日期，格式：yyyy-MM-dd
     * @param deviceSerial 设备序列号
     * @param zipResponse 压缩报告结构体，去除图表分期数据
     * @return
     */
    @Request(
            type = "get",
            url = "/service/sleepDetector/v3/third/whst/sleepReport/list",
            headers = {
                    "Content-Type:application/json",
                    "accessToken:{accessToken}"
            }
    )
    JSONObject sleepReportList(@Var("accessToken") String accessToken, @Query("startDate")String startDate,
                               @Query("endDate")String endDate,@Query("deviceSerial")String deviceSerial,
                               @Query("zipResponse")Boolean zipResponse

    );


    /**
     * 获取设备在离床消息（GET）
     * @param accessToken
     * @param deviceSerial 设备序列号
     * @param offset 偏移量
     * @param limit 限制条数
     * @return
     */
    @Request(
            type = "get",
            url = "/service/sleepDetector/v3/third/whst/statistics/data/bodyDetect",
            headers = {
                    "Content-Type:application/json",
                    "accessToken:{accessToken}"
            }
    )
    JSONObject bodyDetect(@Var("accessToken") String accessToken, @Query("deviceSerial")String deviceSerial,
                               @Query("endDate")Integer offset,@Query("deviceSerial")Integer limit
    );





}
