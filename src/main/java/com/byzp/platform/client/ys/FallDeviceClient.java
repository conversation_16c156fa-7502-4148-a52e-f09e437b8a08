package com.byzp.platform.client.ys;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Request;
import com.dtflys.forest.annotation.Var;

public interface FallDeviceClient {

    /**
     * 查询有人进入检测使能
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/HumanDetection/InDetectEnabled"
    )
    JSONObject queryInDetectEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置有人进入检测使能
     * @param accessToken
     * @param deviceSerial
     * @param data
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/HumanDetection/InDetectEnabled"
    )
    JSONObject setInDetectEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body boolean data);

    /**
     * 查询有人进入告警消息通知使能
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/HumanDetection/InAlarmNoticeEnabled"
    )
    JSONObject queryHumanDetectionAlarmEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置有人进入告警消息通知使能
     * @param accessToken
     * @param deviceSerial
     * @param data
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/HumanDetection/InAlarmNoticeEnabled"
    )
    JSONObject setHumanDetectionAlarmEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body boolean data);

    /**
     * 查询有人进入告警消息上报使能
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/HumanDetection/InAlarmReportEnabled"
    )
    JSONObject queryHumanDetectionAlarmReportEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置有人进入告警消息上报使能
     * @param accessToken
     * @param deviceSerial
     * @param data
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/HumanDetection/InAlarmReportEnabled"
    )
    JSONObject setHumanDetectionAlarmReportEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body boolean data);

    /**
     * 查询跌倒检测规则
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/FallingDownDetection/FallingDownDetectionRule"
    )
    JSONObject queryFallingDownDetectionRule(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置跌倒检测规则
     * @param accessToken
     * @param deviceSerial
     * @param data
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/FallingDownDetection/FallingDownDetectionRule"
    )
    JSONObject setFallingDownDetectionRule(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,
                                           @Body("leftDistance") Integer leftDistance, @Body("frontDistance") Integer frontDistance,
                                           @Body("rightDistance") Integer rightDistance);


    /**
     * 查询有人跌倒消息通知使能
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/FallingDownDetection/PeopleFallingDownNoticeEnabled"
    )
    JSONObject queryPeopleFallingDownNoticeEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置有人跌倒消息通知使能
     * @param accessToken
     * @param deviceSerial
     * @param data
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/FallingDownDetection/PeopleFallingDownNoticeEnabled"
    )
    JSONObject setPeopleFallingDownNoticeEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body boolean data);


    /**
     * 查询最后一条信息
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/FallingDownDetection/RecentNotice"
    )
    JSONObject queryRecentNotice(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置最后一条信息
     * @param accessToken
     * @param deviceSerial
     * @param data
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/FallingDownDetection/RecentNotice"
    )
    JSONObject setRecentNotice(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body Integer data);


    /**
     * 查询网络状态
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/WifiStatus/NetStatus"
    )
    JSONObject netStatus(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);



    /**
     * 查询有人跌倒告警上报使能
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/PeopleFallingDownDetection/PeopleFallingDownReportEnabled"
    )
    JSONObject queryPeopleFallingDownReportEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置有人跌倒告警上报使能
     * @param accessToken
     * @param deviceSerial
     * @param data
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/PeopleFallingDownDetection/PeopleFallingDownReportEnabled"
    )
    JSONObject setPeopleFallingDownReportEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body("enabled") boolean data);


    /**
     * 查询滞留时长
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/stay/stayTime"
    )
    JSONObject queryStayTime(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置滞留时长
     * @param accessToken
     * @param deviceSerial
     * @param data
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/stay/stayTime"
    )
    JSONObject setStayTime(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body Integer data);

    /**
     * 查询滞留检测使能
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/stay/stayEnable"
    )
    JSONObject queryStayEnable(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置滞留检测使能
     * @param accessToken
     * @param deviceSerial
     * @param data
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/stay/stayEnable"
    )
    JSONObject setStayEnable(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body boolean data);
}
