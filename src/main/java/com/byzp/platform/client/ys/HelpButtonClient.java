package com.byzp.platform.client.ys;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Request;
import com.dtflys.forest.annotation.Var;

public interface HelpButtonClient {

    /**
     * 查询是否启用紧急按钮开关
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/HelpButton/EmergencySwitch"
    )
    JSONObject queryEmergencySwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置是否启用紧急按钮开关
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/HelpButton/EmergencySwitch"
    )
    JSONObject setEmergencySwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body boolean data);


    /**
     * 一键消音
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/action/{deviceSerial}/global/0/HelpButton/Erasure"
    )
    JSONObject erasure(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body String body);

    /**
     * 查询剩余电量
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/BatteryInfo/SurplusPower"
    )
    JSONObject querySurplusPower(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置剩余电量
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/BatteryInfo/SurplusPower"
    )
    JSONObject setSurplusPower(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body Integer data);


    /**
     * 查询提示音音量
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/PromptToneVolume"
    )
    JSONObject queryPromptToneVolume(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置提示音音量
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/PromptToneVolume"
    )
    JSONObject setPromptToneVolume(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body Integer data);

    /**
     * 查询提醒使能开关
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/RemindEnabled"
    )
    JSONObject queryRemindEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置提醒使能开关
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/RemindEnabled"
    )
    JSONObject setRemindEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body boolean data);

    /**
     * 查询提示音类型
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/SoundTypes"
    )
    JSONObject querySoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置提示音类型
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/SoundTypes"
    )
    JSONObject setSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body int data);

    /**
     * 查询自定义声音
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/CustomSound"
    )
    JSONObject queryCustomSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置自定义声音
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/CustomSound"
    )
    JSONObject setCustomSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body String data);

    /**
     * 查询告警声音使能开关
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/AlarmSoundEnabled"
    )
    JSONObject queryAlarmSoundEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置告警声音使能开关
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/AlarmSoundEnabled"
    )
    JSONObject setAlarmSoundEnabled(@Var("accessToken") String accessToken,
                                    @Var("deviceSerial") String deviceSerial,
                                    @Body("enabled") boolean enabled,
                                    @Body("soundType") int soundType);

    /**
     * 播放指定声音
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/PlaySpecificSound"
    )
    JSONObject playSpecificSound(@Var("accessToken") String accessToken,
                                 @Var("deviceSerial") String deviceSerial,
                                 @Body("volume") int volume,
                                 @Body("index") int index,
                                 @Body("playSoundType") String playSoundType);

    /**
     * 查询网关自定义声音
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/CustomSound"
    )
    JSONObject queryGatewayCustomSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置网关自定义声音
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/CustomSound"
    )
    JSONObject setGatewayCustomSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body String data);


    /**
     * 查询网关提醒开关
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/GateWayRemindSwitch"
    )
    JSONObject queryGatewayRemindEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置网关提醒开关
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/GateWayRemindSwitch"
    )
    JSONObject setGatewayRemindEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body boolean data);


    /**
     * 查询网关提示音类型
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/SoundTypes"
    )
    JSONObject queryGatewaySoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置网关提示音类型
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/SoundTypes"
    )
    JSONObject setGatewaySoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body String data);


    /**
     * 查询网关声音提醒状态
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "get",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/SoundRemindStatus"
    )
    JSONObject querySoundRemindStatus(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置网关声音提醒状态
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "put",
            headers = {
                    "Content-Type: application/json",
                    "accessToken: {accessToken}"
            },
            url = "#{ys.apiUrl}/v3/otap/prop/{deviceSerial}/global/0/GatewayRemind/SoundRemindStatus"
    )
    JSONObject setSoundRemindStatus(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial, @Body boolean data);
}
