package com.byzp.platform.client.ys;


import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.BaseRequest;
import com.dtflys.forest.annotation.Body;
import com.dtflys.forest.annotation.Request;
import com.dtflys.forest.annotation.Var;

/**
 * 智能门窗传感器（门磁）
 */
@BaseRequest(
        baseURL = "#{ys.apiUrl}",
        headers = {
                "Content-Type: application/json",
                "accessToken: {accessToken}"
        }
)
public interface DoorStatusClient {


    /**
     * 查询工作状态
     *
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/DoorStatus"
    )
    JSONObject selectDoorStatus(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置工作状态
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data  开关
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/DoorStatus"
    )
    JSONObject setDoorStatus(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body boolean data);


    /**
     * 查询未闭合检测时间
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/OpenDetectionTime"
    )
    JSONObject selectOpenDetectionTime(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置未闭合检测时间
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 未闭合检测时间 0-关闭 1-1分钟 2-2分钟 5-5分钟
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/OpenDetectionTime"
    )
    JSONObject setOpenDetectionTime(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);


    /**
     * 查询闭合提醒开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/ClosedRemindSwitch"
    )
    JSONObject selectClosedRemindSwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置闭合提醒开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 闭合提醒开关
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/ClosedRemindSwitch"
    )
    JSONObject setClosedRemindSwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Boolean data);


    /**
     * 查询闭合-网关提醒开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/ClosedGateWaySwitch"
    )
    JSONObject selectClosedGateWaySwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置闭合-网关提醒开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 闭合-网关提醒开关
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/ClosedGateWaySwitch"
    )
    JSONObject setClosedGateWaySwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Boolean data);



    /**
     * 查询闭合自定义声音类型
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/ClosedCustomSoundTypes"
    )
    JSONObject selectClosedCustomSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);


    /**
     * 设置闭合自定义声音类型
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 音效 1-叮咚 2-有人闯入 3-欢迎光临 4-请随时关门 5-请注意安全
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/ClosedCustomSoundTypes"
    )
    JSONObject setClosedCustomSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);


    /**
     * 查询闭合-音效
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/ClosedSound"
    )
    JSONObject selectClosedSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置闭合-音效
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 闭合-音效 0-告警音 1-自定义语音
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/ClosedSound"
    )
    JSONObject setClosedSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);


    /**
     * 查询未闭合提醒开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/OpenRemindSwitch"
    )
    JSONObject selectOpenRemindSwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置未闭合提醒开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 未闭合提醒开关
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/OpenRemindSwitch"
    )
    JSONObject setOpenRemindSwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Boolean data);


    /**
     * 查询未闭合-网关提醒开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/OpenGateWaySwitch"
    )
    JSONObject selectOpenGateWaySwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置未闭合-网关提醒开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 未闭合-网关提醒开关
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/OpenGateWaySwitch"
    )
    JSONObject setOpenGateWaySwitch(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Boolean data);


    /**
     * 查询未闭合自定义声音类型
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/OpenCustomSoundTypes"
    )
    JSONObject selectOpenCustomSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置未闭合自定义声音类型
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 音效  1-叮咚 2-有人闯入 3-欢迎光临 4-请随时关门 5-请注意安全
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/OpenCustomSoundTypes"
    )
    JSONObject setOpenCustomSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);



    /**
     * 查询未闭合音效
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/OpenSound"
    )
    JSONObject selectOpenSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置未闭合音效
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 未闭合音效  0-告警音 1-自定义语音
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/DoorMagnetic/OpenSound"
    )
    JSONObject setOpenSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);



    /**
     * 消音
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/action/{deviceSerial}/global/0/DoorMagnetic/Erasure"
    )
    JSONObject erasure(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);



    /**
     * 查询剩余电量
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/BatteryInfo/SurplusPower"
    )
    JSONObject selectSurplusPower(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置剩余电量
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 剩余电量  0-100
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/BatteryInfo/SurplusPower"
    )
    JSONObject setSurplusPower(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Integer data);


    /**
     * 查询提示音音量
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/PromptToneVolume"
    )
    JSONObject selectPromptToneVolume(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置提示音音量
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 提示音音量
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/PromptToneVolume"
    )
    JSONObject setPromptToneVolume(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Integer data);




    /**
     * 查询提醒使能开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/RemindEnabled"
    )
    JSONObject selectRemindEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置提醒使能开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 提醒使能开关
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/RemindEnabled"
    )
    JSONObject setRemindEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body Boolean data);



    /**
     * 查询提示音类型
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/SoundTypes"
    )
    JSONObject selectSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置提示音类型
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 提示音类型 0-告警音 1-自定义语音
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/SoundTypes"
    )
    JSONObject setSoundTypes(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);




    /**
     * 查询自定义声音
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/CustomSound"
    )
    JSONObject selectCustomSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置自定义声音
     * @param accessToken
     * @param deviceSerial 设备号
     * @param data 音效 1-叮咚 2-有人闯入 3-欢迎光临 4-请随时关门 5-请注意安全
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/CustomSound"
    )
    JSONObject setCustomSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String data);




    /**
     * 查询告警声音使能开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @return
     */
    @Request(
            type = "get",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/AlarmSoundEnabled"
    )
    JSONObject selectAlarmSoundEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial);

    /**
     * 设置告警声音使能开关
     * @param accessToken
     * @param deviceSerial 设备号
     * @param param  voiceIndex(integer) 自定义语音的索引  enabled(boolean) 声音开关  soundType(integer) 声音类型 0-短叫 1-长叫 2-静音 3-自定义语音
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/prop/{deviceSerial}/global/0/SoundSetting/AlarmSoundEnabled"
    )
    JSONObject setAlarmSoundEnabled(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String param);



    /**
     * 播放指定声音
     * @param accessToken
     * @param deviceSerial 设备号
     * @param param  volume(integer) 音量 0-100  playSoundType(String) 声音类型 alarm-告警音 call-呼叫音  index(integer) 声音索引
     * @return
     */
    @Request(
            type = "put",
            url = "/v3/otap/action/{deviceSerial}/global/0/SoundSetting/PlaySpecificSound"
    )
    JSONObject playSpecificSound(@Var("accessToken") String accessToken, @Var("deviceSerial") String deviceSerial,@Body String param);







}
