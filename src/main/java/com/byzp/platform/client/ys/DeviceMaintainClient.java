package com.byzp.platform.client.ys;

import com.alibaba.fastjson.JSONObject;
import com.dtflys.forest.annotation.Request;
import com.dtflys.forest.annotation.Var;

public interface DeviceMaintainClient {

    /**
     * 设置设备撤/布防
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "post",
            url = "#{ys.apiUrl}/lapp/device/defence/set?accessToken={accessToken}&deviceSerial={deviceSerial}&isDefence={isDefence}"
    )
    JSONObject setDefence(@Var("accessToken") String accessToken,
                                @Var("deviceSerial") String deviceSerial,
                                @Var("isDefence") int isDefence);

    /**
     * 获取设备版本信息
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "post",
            url = "#{ys.apiUrl}/lapp/device/version/info?accessToken={accessToken}&deviceSerial={deviceSerial}"
    )
    JSONObject versionInfo(@Var("accessToken") String accessToken,
                           @Var("deviceSerial") String deviceSerial);


    /**
     * 设备升级固件
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "post",
            url = "#{ys.apiUrl}/lapp/device/upgrade?accessToken={accessToken}&deviceSerial={deviceSerial}"
    )
    JSONObject upgrade(@Var("accessToken") String accessToken,
                           @Var("deviceSerial") String deviceSerial);

    /**
     * 获取设备升级状态
     * @param accessToken
     * @param deviceSerial
     * @return
     */
    @Request(
            type = "post",
            url = "#{ys.apiUrl}/lapp/device/upgrade/status?accessToken={accessToken}&deviceSerial={deviceSerial}"
    )
    JSONObject upgradeStatus(@Var("accessToken") String accessToken,
                       @Var("deviceSerial") String deviceSerial);
}
