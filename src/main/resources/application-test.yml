server:
  port: 8001
kylin:
  core:
    defaultVersion: 1
    openApi:
      title: Demo Service
      description: "这是一个测试服务"
      version: 1.0
      contact:
        name: <PERSON>
        email: <EMAIL>


feign:
  client:
    config:
      default:
        connectTimeout: 30000
        readTimeout: 30000
        #        使用默认重试器 最大请求次数为5，初始间隔时间为100ms，下次间隔时间1.5倍递增，重试间最大间隔时间为1s:
#        retryer: feign.Retryer.Default

multipart:
  max-file-size: 200MB
  max-request-size: 200MB
spring:
  main:
    allow-circular-references: true

  #  data:
  #   elasticsearch:
  #     rest:
  #       username: elastic
  #       password: XiaoPinChuXing@2021
  #       uris: http://**************:30903/
  #       connection-timeout: 1000
  #       read-timeout: 1000

  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    username: root
    password: xiaopin88
    url: ***************************************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    initialSize: 10
    minIdle: 10
    maxActive: 20
    maxWait: 60000
    timeBetweenEvictionRunsMillis: 60000
    minEvictableIdleTimeMillis: 300000
    validationQuery: SELECT 1
    testWhileIdle: true
    testOnBorrow: false
    testOnReturn: false
  #    dynamic:
  #      primary: master #设置默认的数据源或者数据源组,默认值即为master
  #      strict: false #严格匹配数据源,默认false. true未匹配到指定数据源时抛异常,false使用默认数据源
  #      datasource:
  #        slave1:
  #          url: **************************************************************************************************************************************************************************
  #          username: root
  #          password: xiaopin88
  #          driver-class-name: com.mysql.jdbc.Driver # 3.2.0开始支持SPI可省略此配置
  #  shardingsphere:
  #    props:
  #      sql.show: true
  #    datasource:
  #      names: ds1
  #      ds1:
  #        type: com.zaxxer.hikari.HikariDataSource
  #        driver-class-name: com.mysql.jdbc.Driver
  #        jdbc-url: *****************************************************************************************************************************************************************************
  #        username: root
  #        password: snin125
  #    sharding:
  #      tables:
  #        user_info:
  #          actual-data-nodes: ds1.user_info_${0..9}
  #          table-strategy:
  #            inline:
  #              sharding-column: id
  #              algorithm-expression: user_info_${id % 10}
  redis:
    host: 127.0.0.1
    port: 6379
    database: 0  #5作为测试用途
    app: xpky
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  application:
    name: xiaopinElderlyCare
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
  elasticsearch:
    uris: 1
    username:
    password:


management:
  endpoint:
    health:
      enabled: false

wxsmallconfig:
  appid: wx5c016341725e789f
  secret: c3e2e191fb001045f52a5eb93a0b4d98
  grant_type: authorization_code
  miniprogram_state: trial

iden_authen:
  appCode:  42dc384858f54d97918270f585cb1898

index:
  user:
alibaba:
  smsTemplateCodeRegist: SMS_286250259
  smsTemplateCodeBack: SMS_286250259
  smsTemplateCodeCoupons: SMS_286250259
sms:
  timeout: 10



#  max-retry-count: 3
#  max-retry-interval: 1000
app:
  app:
    url:
    login:
    loginByMobile:
    wxLoginSpeed:
    wxLoginSpeedNew:
  #老系统的接口
  xpcx:
    giveMallUserRedUrl: http://**************:9166/red_envelope/giveMallUserRed
    voucherExchangeBalanceUrl: http://**************:9166/balance_refund/voucherExchangeBalance
  token:
    url:

wxmsg:
  token-url:
  wx-url:
  miniprogram_state:
  #租车成功模板id
  rent_success_template_id:
  #租车订单支付成功id
  rent_pay_success_template_id:
  return_success_template_id:

#个推
gt:
  appId: eEyK3H3lPgAXOHU9mZW8JA
  appKey: JJklzwAFeKA2eEkU2Ttay9
  masterSecret: wLWIb1OAIb9Ghxfi0KJSi9

wxpay:
  notify_url: http://**************:8711/notify/wechat
  refund_cert_path: /xpkyzs/apiclient_cert.p12
  access_token_url: http://*************:8889/wx/getTokenByKey?key=xpkyWeiXinAccessToken
  need_upload_ship: true

#配置导出功能可以导出的最大条数
sys:
  export:
    max-limit: 20000

kuaidi100:
  key: EtwRwetM5246
  secret: ba56f9b5dff947d5a9dbdacef517875c
  env: test
  orderCallBackUrl: https://ddnk.pin-bike.com/api/express/orderCallBack
  pushCallBackUrl: https://ddnk.pin-bike.com/api/express/pushCallBack
  callBackSalt: didinika@2024
  customer: 1B5546EE98B5B42421461486280ECDE5

jst:
  appKey: ad342eb0d9b64343b24a2833557492cc
  appSecret: a0392c79aad248d48ea7df068d22a4df
  queryStockUrl: https://dev-api.jushuitan.com/open/inventory/query
  uploadOrderUrl: https://dev-api.jushuitan.com/open/jushuitan/orders/upload
  getInitTokenUrl: https://dev-api.jushuitan.com/openWebIsv/auth/getInitToken
  refreshTokenUrl: https://dev-api.jushuitan.com/openWebIsv/auth/refreshToken
  shopId: 10403024
  execute: false


appsecret:
  #老系统的安全密钥
  xpcx: ba88fe66-830a-8bb2-ba20-6643d8876f6f
  #小品商城的安全密钥
  xpsc: 4b6a949a-e1f5-ec2c-aafd-e4a4cf38d932

#萤石开放平台的key和secret
ys:
  appKey: 850848b58f5949999d0f32fef6c554dd
  secret: aa802a75c8eaddd11462c8b6c8cb61e4
  apiUrl: https://open.ys7.com/api
#百度人脸识别
aip:
  appId: 117977665
  appKey: i7RXeARmb6DMWtjrqFNBWjLg
  secretKey: 2iYA2LhlnkCA29zw9rwmsp8FKB2VkPRj
  groupId: xpky

mqtt:
  caPath: D:\cert\emqxsl-ca.crt
  topic: $queue/server/msgTest
  qos: 2
  #重连的最大重试次数
  maxRetryAttempts: 5
  #初始重试延迟(毫秒)
  initialRetryDelay: 1000

audio:
  filePath: /media/

forest:
  max-connections: 1000
  connect-timeout: 30000
  read-timeout: 30000