<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.byzp.platform.mapper.CloudLocationMapper">
  <resultMap id="BaseResultMap" type="com.byzp.platform.mapper.po.CloudLocation">
    <!--@mbg.generated-->
    <!--@Table role_resource_rel-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="application_id" jdbcType="BIGINT" property="applicationId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
  </resultMap>
  <resultMap id="cloudLocationDTO" type="com.byzp.platform.dto.CloudLocationDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="application_id" jdbcType="BIGINT" property="cloudApplicationId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <collection property="groupList" javaType="java.util.List" ofType="com.byzp.platform.dto.CloudLocationGroupDTO">
      <id column="group_id" property="id" />
      <result column="group_name" property="name" />
      <result column="group_remark" property="remark" />
    </collection>
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, role_id, resource_id, create_time, update_time
  </sql>
  <select id="queryList" resultType="com.byzp.platform.dto.CloudLocationDTO">
    select
      a.id,
      a.name,
      a.remark
    from
      cloud_location a
    <where>
      <if test="param.userId != null">
        and a.user_id = #{param.userId}
      </if>
      <if test="param.cloudApplicationId != null">
        and a.cloud_application_id = #{param.cloudApplicationId}
      </if>
      <if test="param.id != null">
        and a.id = #{param.id}
      </if>
    </where>
    order by a.id desc
  </select>
  <select id="queryPage" resultType="com.byzp.platform.dto.CloudLocationDTO">
    select
      a.id,
      a.name,
      a.remark,
      a.create_time
    from
      cloud_location a
      inner join cloud_application c on a.cloud_application_id = c.id
    where
      a.cloud_application_id = #{param.cloudApplicationId}
    <if test="param.name != null and param.name != ''">
      and a.name like concat ('%',#{param.name},'%')
    </if>
    <if test="param.createTimeStart != null and param.createTimeStart != ''">
      and a.create_time &gt;= #{param.createTimeStart}
    </if>
    <if test="param.createTimeEnd != null and param.createTimeEnd != ''">
      and a.create_time &lt;= #{param.createTimeEnd}
    </if>
    order by a.id desc
  </select>
</mapper>