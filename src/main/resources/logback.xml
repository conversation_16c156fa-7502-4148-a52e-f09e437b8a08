<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>

    <property name="BaseDir" value="${log.dir:-logs}"/>


    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="org.apache.skywalking.apm.toolkit.log.logback.v1.x.TraceIdPatternLogbackLayout">
                <Pattern>[%sw_ctx] [%level] %d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %logger:%line - %msg%n</Pattern>
            </layout>
        </encoder>
        <file>${BaseDir}/server/app.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${BaseDir}/server/app-%d{MM-dd-yyyy}-%i.gz</fileNamePattern>
            <!--单个文件的最大容量 超过就记录下一个文件-->
            <maxFileSize>50MB</maxFileSize>
            <!--日志保留的最大时间  设置0 就不会删除旧日志-->
            <maxHistory>0</maxHistory>
            <!--总日志保留的最大容量 超过容量就删除旧文件-->
<!--            <totalSizeCap>100MB</totalSizeCap>-->
        </rollingPolicy>
    </appender>


    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">

        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>


    <logger name="org.springframework" level="info"/>
    <logger name="org.mybatis" level="info"/>
    <logger name="com.byzp" level="debug"/>

    <root level="info">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="RollingFile"/>
    </root>
</configuration>