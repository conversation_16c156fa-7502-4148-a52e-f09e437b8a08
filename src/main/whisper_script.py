import sys
import whisper
import subprocess
import os

input_path = sys.argv[1]
output_dir = "C:\\Users\\<USER>\\Downloads\\"
os.makedirs(output_dir, exist_ok=True)

model = whisper.load_model("small")
result = model.transcribe(input_path, fp16=False)

srt_path = os.path.join(output_dir, "output.srt")
with open(srt_path, "w", encoding="utf-8") as f:
    for segment in result['segments']:
        f.write(f\"{segment['id'] + 1}\\n\")
        start = segment['start']
        end = segment['end']
        text = segment['text']
        f.write(f\"{int(start/3600):02}:{int((start%3600)/60):02}:{int(start%60):02},000 --> {int(end/3600):02}:{int((end%3600)/60):02}:{int(end%60):02},000\\n\")
        f.write(f\"{text}\\n\\n\")

# 合成字幕与视频
output_path = os.path.join(output_dir, \"output.mp4\")
subprocess.run([\"ffmpeg\", \"-y\", \"-i\", input_path, \"-vf\", f\"subtitles={srt_path}\", output_path])
