package com.byzp.platform.service;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.dto.DeviceConfigDTO;
import com.byzp.platform.utils.DeviceConfigWaitManager;
import com.byzp.platform.utils.MqttUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CloudDeviceServiceImplTest {

    @Mock
    private DeviceConfigWaitManager deviceConfigWaitManager;

    @Mock
    private MqttUtil mqttUtil;

    @InjectMocks
    private CloudDeviceServiceImpl cloudDeviceService;

    @Test
    void testQueryDeviceConfig_Success() throws Exception {
        // 准备测试数据
        String deviceSerial = "TEST_DEVICE_001";
        DeviceConfigDTO expectedConfig = new DeviceConfigDTO();
        expectedConfig.setDeviceSerial(deviceSerial);
        expectedConfig.setSmStartTime("22:00");
        expectedConfig.setSmEndTime("08:00");
        expectedConfig.setVersion("1.0.0");
        expectedConfig.setXlWarnMax(120);
        expectedConfig.setXlWarnMin(60);

        // 创建一个已完成的Future
        CompletableFuture<DeviceConfigDTO> completedFuture = CompletableFuture.completedFuture(expectedConfig);

        // 模拟行为
        when(deviceConfigWaitManager.createWaitingRequest(deviceSerial)).thenReturn(completedFuture);
        doNothing().when(mqttUtil).sendMsgToDevice(eq(deviceSerial), anyString());

        // 执行测试
        DeviceConfigDTO result = cloudDeviceService.queryDeviceConfig(deviceSerial);

        // 验证结果
        assertNotNull(result);
        assertEquals(deviceSerial, result.getDeviceSerial());
        assertEquals("22:00", result.getSmStartTime());
        assertEquals("08:00", result.getSmEndTime());
        assertEquals("1.0.0", result.getVersion());
        assertEquals(120, result.getXlWarnMax());
        assertEquals(60, result.getXlWarnMin());

        // 验证方法调用
        verify(deviceConfigWaitManager).createWaitingRequest(deviceSerial);
        verify(mqttUtil).sendMsgToDevice(eq(deviceSerial), anyString());
    }

    @Test
    void testQueryDeviceConfig_Timeout() {
        // 准备测试数据
        String deviceSerial = "TEST_DEVICE_002";

        // 创建一个会超时的Future
        CompletableFuture<DeviceConfigDTO> timeoutFuture = new CompletableFuture<>();
        timeoutFuture.orTimeout(1, TimeUnit.MILLISECONDS); // 设置很短的超时时间

        // 模拟行为
        when(deviceConfigWaitManager.createWaitingRequest(deviceSerial)).thenReturn(timeoutFuture);
        doNothing().when(mqttUtil).sendMsgToDevice(eq(deviceSerial), anyString());
        doNothing().when(deviceConfigWaitManager).cancelRequest(deviceSerial);

        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            cloudDeviceService.queryDeviceConfig(deviceSerial);
        });

        // 验证清理方法被调用
        verify(deviceConfigWaitManager).cancelRequest(deviceSerial);
    }
}
