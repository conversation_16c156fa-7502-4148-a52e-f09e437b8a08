package com.byzp.platform.utils;

import com.alibaba.fastjson.JSONObject;
import com.byzp.platform.dto.DeviceConfigDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

class DeviceConfigWaitManagerTest {

    private DeviceConfigWaitManager waitManager;

    @BeforeEach
    void setUp() {
        waitManager = new DeviceConfigWaitManager();
    }

    @Test
    void testCreateWaitingRequest() {
        String deviceSerial = "TEST_DEVICE_001";
        
        CompletableFuture<DeviceConfigDTO> future = waitManager.createWaitingRequest(deviceSerial);
        
        assertNotNull(future);
        assertFalse(future.isDone());
        assertTrue(waitManager.hasWaitingRequest(deviceSerial));
        assertEquals(1, waitManager.getWaitingRequestCount());
    }

    @Test
    void testCompleteRequest() {
        String deviceSerial = "TEST_DEVICE_002";
        
        // 创建等待请求
        CompletableFuture<DeviceConfigDTO> future = waitManager.createWaitingRequest(deviceSerial);
        
        // 准备配置数据
        DeviceConfigDTO configDTO = new DeviceConfigDTO();
        configDTO.setDeviceSerial(deviceSerial);
        configDTO.setSmStartTime("22:00");
        configDTO.setSmEndTime("08:00");
        
        // 完成请求
        boolean completed = waitManager.completeRequest(deviceSerial, configDTO);
        
        assertTrue(completed);
        assertTrue(future.isDone());
        assertFalse(waitManager.hasWaitingRequest(deviceSerial));
        assertEquals(0, waitManager.getWaitingRequestCount());
        
        // 验证结果
        try {
            DeviceConfigDTO result = future.get();
            assertEquals(deviceSerial, result.getDeviceSerial());
            assertEquals("22:00", result.getSmStartTime());
            assertEquals("08:00", result.getSmEndTime());
        } catch (Exception e) {
            fail("获取结果时发生异常: " + e.getMessage());
        }
    }

    @Test
    void testCancelRequest() {
        String deviceSerial = "TEST_DEVICE_003";
        
        // 创建等待请求
        CompletableFuture<DeviceConfigDTO> future = waitManager.createWaitingRequest(deviceSerial);
        
        // 取消请求
        boolean cancelled = waitManager.cancelRequest(deviceSerial);
        
        assertTrue(cancelled);
        assertTrue(future.isCancelled());
        assertFalse(waitManager.hasWaitingRequest(deviceSerial));
        assertEquals(0, waitManager.getWaitingRequestCount());
    }

    @Test
    void testParseDeviceConfig() {
        // 准备测试数据
        JSONObject payloadObject = new JSONObject();
        payloadObject.put("clientId", "TEST_DEVICE_004");
        payloadObject.put("msgType", "read_config");
        
        JSONObject payload = new JSONObject();
        payload.put("smStartTime", 79200); // 22:00 = 22*3600 = 79200秒
        payload.put("smEndTime", 28800);   // 08:00 = 8*3600 = 28800秒
        payload.put("version", "1.0.0");
        payload.put("xlWarnMax", 120);
        payload.put("xlWarnMin", 60);
        payload.put("hxWarnMax", 30);
        payload.put("hxWarnMin", 12);
        payload.put("lcWarnTime", 10);
        
        payloadObject.put("payload", payload);
        
        // 解析配置
        DeviceConfigDTO result = waitManager.parseDeviceConfig(payloadObject);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("TEST_DEVICE_004", result.getDeviceSerial());
        assertEquals("22:00", result.getSmStartTime());
        assertEquals("08:00", result.getSmEndTime());
        assertEquals("1.0.0", result.getVersion());
        assertEquals(120, result.getXlWarnMax());
        assertEquals(60, result.getXlWarnMin());
        assertEquals(30, result.getHxWarnMax());
        assertEquals(12, result.getHxWarnMin());
        assertEquals(10, result.getLcWarnTime());
    }

    @Test
    void testTimeout() throws InterruptedException {
        String deviceSerial = "TEST_DEVICE_005";
        
        // 创建一个很短超时时间的等待请求
        CompletableFuture<DeviceConfigDTO> future = waitManager.createWaitingRequest(deviceSerial, 1);
        
        // 等待超时
        Thread.sleep(2000);
        
        assertTrue(future.isCompletedExceptionally());
        assertFalse(waitManager.hasWaitingRequest(deviceSerial));
        assertEquals(0, waitManager.getWaitingRequestCount());
    }

    @Test
    void testReplaceExistingRequest() {
        String deviceSerial = "TEST_DEVICE_006";
        
        // 创建第一个请求
        CompletableFuture<DeviceConfigDTO> future1 = waitManager.createWaitingRequest(deviceSerial);
        assertFalse(future1.isDone());
        
        // 创建第二个请求（应该取消第一个）
        CompletableFuture<DeviceConfigDTO> future2 = waitManager.createWaitingRequest(deviceSerial);
        
        assertTrue(future1.isCancelled());
        assertFalse(future2.isDone());
        assertTrue(waitManager.hasWaitingRequest(deviceSerial));
        assertEquals(1, waitManager.getWaitingRequestCount());
    }
}
