#  第三方接口回调文档

## 第三方接口调用数据结构模板

### 基础数据结构
```json
{
  "msgId": "随机生成的UUID（去除横线）",
  "msgType": "消息类型",
  "msgTime": "消息时间戳（秒级）",
  "deviceSerial": "设备序列号",
  "data": {
    // 根据不同msgType包含不同的数据结构
  }
}
```

### HTTP 请求头
```
Content-Type: application/json
msgSource: byzp
```

## 不同 msgType 的详细说明

### 1. sleep_monitor - 体征监测数据

**描述**: 设备上报的体征监测数据（35秒一次）

**数据结构**:
```json
{
  "msgId": "a1b2c3d4e5f6g7h8i9j0",
  "msgType": "sleep_monitor",
  "msgTime": 1672531200,
  "deviceSerial": "DEV001",
  "data": {
    "msg": [
      "75",// 心率
      "14",// 呼吸率
      "7" // 大于0表示有人，等于0表示没人
    ]
  }
}
```

### 2. audio_transfer - 语音转文字（声纹识别）

**描述**: 硬件录入的语音经过声纹识别和语音转文字处理后的回调

**数据结构**:
```json
{
  "msgId": "a1b2c3d4e5f6g7h8i9j0",
  "msgType": "audio_transfer",
  "msgTime": 1672531200,
  "deviceSerial": "DEV001",
  "data": {
    "audioText": "你好，我需要帮助",// 语音转文字结果
    "voiceId": "voice_001", // 匹配到的声纹ID
    "mp3Base64": "base64编码的mp3音频数据",// 原始音频的mp3格式base64编码
    "voiceSimilarity": "0.85",// 声纹匹配相似度,越大越相似
    "randNum": "random123" // 随机数标识
  }
}
```

**参数说明**:
- `audioText`: 语音转换后的文字内容
- `voiceId`: 匹配到的声纹ID
- `mp3Base64`: 原始音频的mp3格式base64编码
- `voiceSimilarity`: 声纹匹配相似度（0-1之间）
- `randNum`: 随机数标识


### 3. client.connected - 设备上线

**描述**: 设备连接到平台的通知

**数据结构**:
```json
{
  "msgId": "a1b2c3d4e5f6g7h8i9j0",
  "msgType": "client.connected",
  "msgTime": 1672531200,
  "deviceSerial": "DEV001",
  "data": {}
}
```

### 4. client.disConnected - 设备离线

**描述**: 设备断开连接的通知

**数据结构**:
```json
{
  "msgId": "a1b2c3d4e5f6g7h8i9j0",
  "msgType": "client.disConnected",
  "msgTime": 1672531200,
  "deviceSerial": "DEV001",
  "data": {}
}
```

### 5. lc_warn - 离床告警

**描述**: 检测到用户离床的告警信息

**数据结构**:
```json
{
  "msgId": "a1b2c3d4e5f6g7h8i9j0",
  "msgType": "lc_warn",
  "msgTime": 1672531200,
  "deviceSerial": "DEV001",
  "data": {
    "result": "1"// 1表示离床
  }
}
```

### 6. xl_warn - 心率告警

**描述**: 心率异常告警信息

**数据结构**:
```json
{
  "msgId": "a1b2c3d4e5f6g7h8i9j0",
  "msgType": "xl_warn",
  "msgTime": 1672531200,
  "deviceSerial": "DEV001",
  "data": {
    "result": "1", // 1表示心率异常
    "xl_average": "100" //当前平均心率
  }
}
```

### 7. hx_warn - 呼吸告警

**描述**: 呼吸异常告警信息

**数据结构**:
```json
{
  "msgId": "a1b2c3d4e5f6g7h8i9j0",
  "msgType": "hx_warn",
  "msgTime": 1672531200,
  "deviceSerial": "DEV001",
  "data": {
    "result": "1", // 1表示心率异常
    "hx_average": "100" //当前平均呼吸率
  }
}
```

### 8. voice_record - 声纹录制成功推送
**描述**: 录制声纹成功推送

**数据结构**:
```json
{
  "msgId": "a1b2c3d4e5f6g7h8i9j0",
  "msgType": "voice_record",
  "msgTime": 1672531200,
  "deviceSerial": "DEV001",
  "data": {
    "voiceId": "voice_001" // 请求声纹录制接口时传入的声纹ID
  }
}
```



## 注意事项

1. 所有时间戳均为秒级时间戳
2. `msgId` 为系统自动生成的UUID（去除横线）
3. `audio_transfer` 类型需要先进行声纹匹配，匹配成功后才会进行语音转文字和回调
4. `lc_warn`,`xl_warn`,`hx_warn` 为设备检测到异常后上报的告警信息，如果后续恢复正常，设备不会再次上报恢复的信息
