# 设备配置查询异步等待机制实现

## 概述

本实现为 `CloudDeviceServiceImpl.queryDeviceConfig` 方法添加了异步等待机制，使其能够等待 `MqttCallbackConsumer.messageArrived` 中收到的 `msgType` 为 `read_config` 且 `clientId` 匹配的消息，并将消息内容返回给调用者。

## 实现方案

采用 **CompletableFuture + ConcurrentHashMap** 的异步等待机制：

1. **DeviceConfigWaitManager**: 消息等待管理器，负责管理异步等待请求
2. **CloudDeviceServiceImpl**: 修改 `queryDeviceConfig` 方法，使用等待机制
3. **MqttCallbackConsumer**: 添加 `read_config` 消息处理逻辑

## 核心组件

### 1. DeviceConfigWaitManager

负责管理设备配置查询的异步等待机制：

- 使用 `ConcurrentHashMap` 存储等待中的请求
- 使用 `CompletableFuture` 实现异步等待
- 支持超时处理（默认30秒）
- 支持请求取消和清理
- 提供消息解析功能

### 2. 修改后的 queryDeviceConfig 方法

```java
public DeviceConfigDTO queryDeviceConfig(String deviceSerial){
    try {
        // 创建等待请求
        CompletableFuture<DeviceConfigDTO> future = deviceConfigWaitManager.createWaitingRequest(deviceSerial);
        
        // 发送MQTT消息到设备
        JSONObject msg = new JSONObject();
        msg.put("msgType","read_config");
        msg.put("clientId", deviceSerial);
        mqttUtil.sendMsgToDevice(deviceSerial, msg.toJSONString());
        
        // 等待设备响应，最多等待30秒
        return future.get();
    } catch (Exception e) {
        log.error("查询设备配置失败，deviceSerial: {}", deviceSerial, e);
        // 确保清理等待请求
        deviceConfigWaitManager.cancelRequest(deviceSerial);
        throw new KylinBusinessException(500, "查询设备配置失败: " + e.getMessage());
    }
}
```

### 3. MqttCallbackConsumer 消息处理

在 `messageArrived` 方法中添加了 `read_config` 消息处理逻辑：

```java
} else if ("read_config".equals(payloadObject.getString("msgType"))) {
    // 处理设备配置查询响应
    String deviceSerial = payloadObject.getString("clientId");
    log.info("收到设备配置响应，deviceSerial: {}", deviceSerial);
    
    try {
        DeviceConfigWaitManager waitManager = (DeviceConfigWaitManager) SpringContextUtil.getBean("deviceConfigWaitManager");
        if (waitManager.hasWaitingRequest(deviceSerial)) {
            DeviceConfigDTO configDTO = waitManager.parseDeviceConfig(payloadObject);
            if (configDTO != null) {
                boolean completed = waitManager.completeRequest(deviceSerial, configDTO);
                if (completed) {
                    log.info("成功处理设备配置响应，deviceSerial: {}", deviceSerial);
                } else {
                    log.warn("完成设备配置请求失败，deviceSerial: {}", deviceSerial);
                }
            } else {
                log.error("解析设备配置数据失败，deviceSerial: {}", deviceSerial);
            }
        } else {
            log.warn("未找到等待中的设备配置查询请求，deviceSerial: {}", deviceSerial);
        }
    } catch (Exception e) {
        log.error("处理设备配置响应时发生异常，deviceSerial: {}", deviceSerial, e);
    }
}
```

## 消息格式

### 发送到设备的消息格式

```json
{
    "msgType": "read_config",
    "clientId": "设备序列号"
}
```

### 设备响应的消息格式

```json
{
    "msgType": "read_config",
    "clientId": "设备序列号",
    "payload": {
        "smStartTime": 79200,    // 睡眠监测开始时间（秒）
        "smEndTime": 28800,      // 睡眠监测结束时间（秒）
        "version": "1.0.0",      // 设备版本
        "xlWarnMax": 120,        // 心率异常告警最高值
        "xlWarnMin": 60,         // 心率异常告警最低值
        "hxWarnMax": 30,         // 呼吸异常告警最高值
        "hxWarnMin": 12,         // 呼吸异常告警最低值
        "lcWarnTime": 10         // 离床告警时间（分钟）
    }
}
```

## 特性

1. **异步等待**: 使用 CompletableFuture 实现非阻塞等待
2. **超时处理**: 默认30秒超时，避免无限等待
3. **并发安全**: 使用 ConcurrentHashMap 保证线程安全
4. **自动清理**: 请求完成或超时后自动清理资源
5. **重复请求处理**: 同一设备的新请求会取消之前的请求
6. **异常处理**: 完善的异常处理和日志记录
7. **消息解析**: 自动解析设备响应消息并转换为DTO

## 使用方式

### 1. 直接调用服务方法

```java
@Resource
private CloudDeviceServiceImpl cloudDeviceService;

public void example() {
    try {
        DeviceConfigDTO config = cloudDeviceService.queryDeviceConfig("DEVICE_001");
        System.out.println("设备配置: " + config);
    } catch (Exception e) {
        System.err.println("查询失败: " + e.getMessage());
    }
}
```

### 2. 通过REST API调用

```bash
GET /device/config/query?deviceSerial=DEVICE_001
```

## 测试

项目包含完整的单元测试：

- `CloudDeviceServiceImplTest`: 测试服务层逻辑
- `DeviceConfigWaitManagerTest`: 测试等待管理器功能

运行测试：

```bash
mvn test
```

## 注意事项

1. **超时时间**: 默认30秒超时，可根据实际需要调整
2. **并发限制**: 同一设备同时只能有一个等待中的请求
3. **内存管理**: 等待管理器会自动清理过期和完成的请求
4. **异常处理**: 所有异常都会被捕获并转换为业务异常
5. **日志记录**: 完整的日志记录便于问题排查

## 扩展性

该实现具有良好的扩展性：

1. 可以轻松添加其他类型的异步等待机制
2. 可以调整超时时间和重试策略
3. 可以添加更多的消息解析逻辑
4. 可以集成监控和指标收集

## 性能考虑

1. **内存使用**: 使用 ConcurrentHashMap 存储等待请求，内存占用较小
2. **线程安全**: 所有操作都是线程安全的
3. **超时清理**: 自动清理机制避免内存泄漏
4. **并发处理**: 支持多个设备同时查询配置
